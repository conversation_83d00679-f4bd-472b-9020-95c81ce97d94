<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能数据溯源追踪系统 - D3.js 可视化演示</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 16px;
            color: #718096;
            margin-bottom: 20px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3182ce;
            color: white;
        }

        .btn-primary:hover {
            background: #2c5aa0;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        #visualization {
            width: 100%;
            height: 600px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
            position: relative;
        }

        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node:hover {
            transform: scale(1.1);
        }

        .node-text {
            font-family: 'SF Pro Display', sans-serif;
            font-size: 12px;
            font-weight: 500;
            text-anchor: middle;
            dominant-baseline: middle;
            pointer-events: none;
        }

        .node-label {
            font-family: 'SF Mono', monospace;
            font-size: 10px;
            text-anchor: middle;
            fill: #718096;
            pointer-events: none;
        }

        .link {
            fill: none;
            stroke-width: 3;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .link:hover {
            opacity: 1;
            stroke-width: 4;
        }

        .highlight-path {
            stroke: #ff6b35;
            stroke-width: 4;
            opacity: 0.9;
            animation: flowAnimation 3s linear infinite;
        }

        .data-flow {
            stroke: #00e676;
            stroke-width: 3;
            stroke-dasharray: 12,6;
            animation: dashAnimation 2s linear infinite;
        }

        @keyframes flowAnimation {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -24; }
        }

        @keyframes dashAnimation {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -18; }
        }

        .pulse {
            animation: pulseAnimation 2s infinite;
        }

        @keyframes pulseAnimation {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .metrics-panel {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(59,130,246,0.05);
            border: 1px solid #3b82f6;
            border-radius: 12px;
            padding: 15px;
            min-width: 200px;
        }

        .metrics-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 10px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .metric-label {
            color: #6b7280;
        }

        .metric-value {
            color: #1f2937;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🤖 智能数据溯源追踪系统</h1>
            <p class="subtitle">AI-Powered Data Lineage & Traceability Platform</p>
            <div class="controls">
                <button class="btn btn-primary" onclick="startAnimation()">▶️ 启动数据流动画</button>
                <button class="btn btn-secondary" onclick="highlightPath()">💡 高亮溯源路径</button>
                <button class="btn btn-secondary" onclick="resetVisualization()">🔄 重置视图</button>
                <button class="btn btn-secondary" onclick="toggleMetrics()">📊 性能监控</button>
            </div>
        </div>
        
        <div id="visualization"></div>
        
        <div class="metrics-panel" id="metricsPanel">
            <div class="metrics-title">📈 实时数据流量</div>
            <div class="metric-item">
                <span class="metric-label">当前吞吐量:</span>
                <span class="metric-value" id="throughput">2.3 GB/s</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">延迟:</span>
                <span class="metric-value" id="latency">8ms</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">成功率:</span>
                <span class="metric-value" id="successRate">99.97%</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">活跃节点:</span>
                <span class="metric-value" id="activeNodes">9</span>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 数据定义
        const nodes = [
            { id: 'edge', name: 'Edge IoT', label: '车载传感器', x: 150, y: 200, color: '#ff6b35', size: 25, status: 'active' },
            { id: 'cloud', name: 'Cloud Storage', label: '分布式存储', x: 300, y: 120, color: '#4f46e5', size: 28, status: 'active' },
            { id: 'backup', name: 'Backup', label: '备份存储', x: 300, y: 280, color: '#4f46e5', size: 22, status: 'normal' },
            { id: 'ml', name: 'ML Model', label: '模型训练', x: 450, y: 80, color: '#059669', size: 20, status: 'normal' },
            { id: 'ai', name: 'AI Engine', label: '深度学习处理', x: 450, y: 200, color: '#059669', size: 35, status: 'selected', gpu: '87%' },
            { id: 'feature', name: 'Feature', label: '特征工程', x: 450, y: 320, color: '#059669', size: 20, status: 'normal' },
            { id: 'gateway', name: 'Gateway', label: 'API网关', x: 600, y: 120, color: '#dc2626', size: 18, status: 'normal' },
            { id: 'api', name: 'Secure API', label: '加密传输', x: 750, y: 200, color: '#dc2626', size: 26, status: 'active', security: 'TLS 1.3' },
            { id: 'monitor', name: 'Monitor', label: '监控中心', x: 600, y: 320, color: '#dc2626', size: 18, status: 'normal' },
            { id: 'blockchain', name: 'Blockchain', label: '存证链', x: 900, y: 280, color: '#7c3aed', size: 24, status: 'active', block: '#1247' },
            { id: 'enterprise', name: 'Enterprise', label: '企业接收方', x: 1050, y: 200, color: '#7c3aed', size: 28, status: 'online' }
        ];

        const links = [
            { source: 'edge', target: 'cloud', type: 'main' },
            { source: 'edge', target: 'backup', type: 'backup' },
            { source: 'cloud', target: 'ai', type: 'main' },
            { source: 'cloud', target: 'ml', type: 'normal' },
            { source: 'backup', target: 'feature', type: 'normal' },
            { source: 'ai', target: 'api', type: 'main' },
            { source: 'ai', target: 'gateway', type: 'normal' },
            { source: 'feature', target: 'monitor', type: 'normal' },
            { source: 'api', target: 'enterprise', type: 'main' },
            { source: 'api', target: 'blockchain', type: 'security' },
            { source: 'blockchain', target: 'enterprise', type: 'main' }
        ];

        // 创建SVG
        const width = 1200;
        const height = 600;
        const svg = d3.select('#visualization')
            .append('svg')
            .attr('width', width)
            .attr('height', height);

        // 创建渐变定义
        const defs = svg.append('defs');
        
        // 节点发光效果
        const glowFilter = defs.append('filter')
            .attr('id', 'glow')
            .attr('x', '-50%')
            .attr('y', '-50%')
            .attr('width', '200%')
            .attr('height', '200%');
        
        glowFilter.append('feGaussianBlur')
            .attr('stdDeviation', '3')
            .attr('result', 'coloredBlur');
        
        const feMerge = glowFilter.append('feMerge');
        feMerge.append('feMergeNode').attr('in', 'coloredBlur');
        feMerge.append('feMergeNode').attr('in', 'SourceGraphic');

        // 箭头标记
        defs.append('marker')
            .attr('id', 'arrowhead')
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', 8)
            .attr('refY', 0)
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('fill', '#00e676');

        // 创建连线
        const linkGroup = svg.append('g').attr('class', 'links');
        const nodeGroup = svg.append('g').attr('class', 'nodes');

        // 绘制连线
        const linkElements = linkGroup.selectAll('.link')
            .data(links)
            .enter()
            .append('path')
            .attr('class', 'link')
            .attr('stroke', d => {
                switch(d.type) {
                    case 'main': return '#00e676';
                    case 'security': return '#ff6b35';
                    case 'backup': return '#a0aec0';
                    default: return '#cbd5e0';
                }
            })
            .attr('marker-end', 'url(#arrowhead)')
            .attr('d', d => {
                const source = nodes.find(n => n.id === d.source);
                const target = nodes.find(n => n.id === d.target);
                return `M${source.x},${source.y} Q${(source.x + target.x)/2},${(source.y + target.y)/2 - 30} ${target.x},${target.y}`;
            });

        // 绘制节点
        const nodeElements = nodeGroup.selectAll('.node')
            .data(nodes)
            .enter()
            .append('g')
            .attr('class', 'node')
            .attr('transform', d => `translate(${d.x}, ${d.y})`);

        // 节点圆圈
        nodeElements.append('circle')
            .attr('r', d => d.size)
            .attr('fill', d => d.color)
            .attr('filter', 'url(#glow)')
            .attr('opacity', 0.9);

        // 选中状态的外圈
        nodeElements.filter(d => d.status === 'selected')
            .append('circle')
            .attr('r', d => d.size + 5)
            .attr('fill', 'none')
            .attr('stroke', '#fbbf24')
            .attr('stroke-width', 3)
            .attr('class', 'pulse');

        // 节点文字
        nodeElements.append('text')
            .attr('class', 'node-text')
            .attr('y', 0)
            .text(d => d.name)
            .attr('fill', 'white');

        // 节点标签
        nodeElements.append('text')
            .attr('class', 'node-label')
            .attr('y', d => d.size + 15)
            .text(d => d.label);

        // 特殊指示器
        nodeElements.filter(d => d.gpu)
            .append('text')
            .attr('class', 'node-label')
            .attr('y', -d => d.size - 10)
            .text(d => `GPU: ${d.gpu}`)
            .attr('fill', '#10b981');

        nodeElements.filter(d => d.security)
            .append('text')
            .attr('class', 'node-label')
            .attr('y', -d => d.size - 10)
            .text(d => d.security)
            .attr('fill', '#fbbf24');

        nodeElements.filter(d => d.block)
            .append('text')
            .attr('class', 'node-label')
            .attr('y', -d => d.size - 10)
            .text(d => `Block ${d.block}`)
            .attr('fill', '#10b981');

        // 状态指示器
        nodeElements.filter(d => d.status === 'active' || d.status === 'online')
            .append('circle')
            .attr('cx', d => d.size - 5)
            .attr('cy', -d => d.size + 5)
            .attr('r', 3)
            .attr('fill', '#10b981')
            .attr('class', 'pulse');

        // 工具提示
        const tooltip = d3.select('#tooltip');

        nodeElements
            .on('mouseover', function(event, d) {
                tooltip
                    .style('opacity', 1)
                    .style('left', (event.pageX + 10) + 'px')
                    .style('top', (event.pageY - 10) + 'px')
                    .html(`
                        <strong>${d.name}</strong><br/>
                        类型: ${d.label}<br/>
                        状态: ${d.status}<br/>
                        ${d.gpu ? `GPU利用率: ${d.gpu}<br/>` : ''}
                        ${d.security ? `安全协议: ${d.security}<br/>` : ''}
                        ${d.block ? `区块高度: ${d.block}<br/>` : ''}
                    `);
            })
            .on('mouseout', function() {
                tooltip.style('opacity', 0);
            });

        // 动画函数
        let animationRunning = false;

        function startAnimation() {
            if (animationRunning) return;
            animationRunning = true;
            
            linkElements
                .filter(d => d.type === 'main')
                .classed('data-flow', true)
                .attr('stroke-dasharray', '12,6');
            
            setTimeout(() => {
                linkElements.classed('data-flow', false).attr('stroke-dasharray', 'none');
                animationRunning = false;
            }, 5000);
        }

        function highlightPath() {
            const mainPath = links.filter(d => d.type === 'main');
            linkElements
                .filter(d => d.type === 'main')
                .classed('highlight-path', true)
                .attr('stroke-dasharray', '8,4');
            
            setTimeout(() => {
                linkElements.classed('highlight-path', false).attr('stroke-dasharray', 'none');
            }, 3000);
        }

        function resetVisualization() {
            linkElements
                .classed('highlight-path', false)
                .classed('data-flow', false)
                .attr('stroke-dasharray', 'none');
            animationRunning = false;
        }

        function toggleMetrics() {
            const panel = document.getElementById('metricsPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 实时更新指标
        function updateMetrics() {
            document.getElementById('throughput').textContent = (2.1 + Math.random() * 0.4).toFixed(1) + ' GB/s';
            document.getElementById('latency').textContent = Math.floor(6 + Math.random() * 4) + 'ms';
            document.getElementById('successRate').textContent = (99.95 + Math.random() * 0.04).toFixed(2) + '%';
        }

        setInterval(updateMetrics, 2000);

        // 初始动画
        setTimeout(() => {
            startAnimation();
        }, 1000);
    </script>
</body>
</html>
