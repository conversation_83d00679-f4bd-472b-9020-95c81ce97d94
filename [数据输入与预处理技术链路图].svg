<svg xmlns="http://www.w3.org/2000/svg" width="900" height="280" viewBox="0 0 900 280">
  <style>
    .block { fill: white; stroke: black; stroke-width: 1; rx: 5; ry: 5; }
    .dashed-block { fill: none; stroke: black; stroke-width: 1; stroke-dasharray: 5,5; }
    .text { font-family: sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
    .subtext { font-family: sans-serif; font-size: 10px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
    .title { font-family: sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>

  <text x="450" y="30" class="title">实时数据处理管道 (Flink DataStream API)</text>

  <!-- 原始数据流 -->
  <rect x="10" y="100" width="100" height="60" class="block"/>
  <text x="60" y="115" class="text">原始数据流</text>
  <text x="60" y="135" class="subtext">车端/企业端</text>
  <text x="60" y="148" class="subtext">二进制数据</text>

  <!-- 原始数据流 -> 协议解析 -->
  <line x1="110" y1="130" x2="130" y2="130" class="arrow"/>

  <!-- 协议解析 -->
  <rect x="130" y="90" width="150" height="80" class="block"/>
  <text x="205" y="105" class="text">协议解析 (Netty)</text>
  <text x="205" y="125" class="subtext">高性能协议解析器</text>
  <text x="205" y="140" class="subtext">Zero-Copy 技术</text>
  <text x="205" y="155" class="subtext">ByteBuf 对象池</text>

  <!-- 协议解析 -> 数据预处理 -->
  <line x1="280" y1="130" x2="300" y2="130" class="arrow"/>

  <!-- Flink Processing Container -->
  <rect x="300" y="70" width="450" height="140" class="dashed-block"/>

  <!-- 数据预处理 -->
  <rect x="320" y="100" width="120" height="80" class="block"/>
  <text x="380" y="115" class="text">数据预处理</text>
  <text x="380" y="135" class="subtext">数据清洗</text>
  <text x="380" y="150" class="subtext">格式转换</text>
  <text x="380" y="165" class="subtext">质量校验</text>

  <!-- 数据预处理 -> 实时分析与性能优化 -->
  <line x1="440" y1="140" x2="460" y2="140" class="arrow"/>

  <!-- 实时分析与性能优化 -->
  <rect x="460" y="90" width="270" height="100" class="block"/>
  <text x="595" y="110" class="text">实时分析与性能优化</text>
  <text x="595" y="135" class="subtext">时间窗口聚合 (识别异常行为)</text>
  <text x="595" y="155" class="subtext">背压处理 (流量控制)</text>
  <text x="595" y="175" class="subtext">并行化处理 (多核利用)</text>

  <!-- 实时分析与性能优化 -> 标准化事件流 -->
  <line x1="730" y1="140" x2="770" y2="140" class="arrow"/>

  <!-- 标准化事件流 -->
  <rect x="770" y="110" width="120" height="60" class="block"/>
  <text x="830" y="125" class="text">标准化事件流</text>
  <text x="830" y="145" class="subtext">To 风险识别引擎</text>
</svg>