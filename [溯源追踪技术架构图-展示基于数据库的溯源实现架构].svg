<svg xmlns="http://www.w3.org/2000/svg" width="800" height="380" viewBox="0 0 800 380">
  <style>
    .db-cylinder { fill: #ADD8E6; stroke: black; stroke-width: 1; }
    .table-box { fill: white; stroke: black; stroke-width: 1; rx: 3; ry: 3; }
    .text { font-family: sans-serif; font-size: 12px; }
    .title { font-family: sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
    .highlight { fill: #FFFFE0; stroke: #FFD700; stroke-width: 1; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>

  <text x="400" y="30" class="title">基于关系型数据库的溯源技术架构 (PostgreSQL)</text>

  <!-- Data Sources -->
  <rect x="30" y="100" width="100" height="50" class="table-box"/>
  <text x="40" y="120" class="text">车端数据采集</text>
  <text x="40" y="135" class="text">(生成Log_ID_1)</text>

  <rect x="30" y="200" width="100" height="50" class="table-box"/>
  <text x="40" y="225" class="text">企业端数据处理</text>

  <line x="130" y="125" x2="180" y2="125" class="arrow"/>
  <line x="130" y="225" x2="180" y2="225" class="arrow"/>

  <!-- PostgreSQL Database -->
  <g transform="translate(180, 70)">
    <ellipse cx="220" cy="30" rx="220" ry="20" class="db-cylinder"/>
    <rect x="0" y="30" width="440" height="200" class="db-cylinder"/>
    <ellipse cx="220" cy="230" rx="220" ry="20" class="db-cylinder" style="fill-opacity: 0;"/>
    <text x="220" y="45" class="text" style="text-anchor: middle;">溯源数据库 (PostgreSQL)</text>

    <!-- Tables -->
    <!-- Vehicle Processing Logs -->
    <rect x="30" y="70" width="180" height="70" class="table-box"/>
    <text x="40" y="85" class="text" style="font-weight: bold;">vehicle_processing_logs</text>
    <rect x="40" y="95" width="100" height="20" class="highlight"/>
    <text x="45" y="110" class="text">log_id (PK) = Log_ID_1</text>
    <text x="40" y="130" class="text">VIN, Timestamp...</text>

    <!-- Enterprise Processing Logs -->
    <rect x="230" y="140" width="180" height="80" class="table-box"/>
    <text x="240" y="155" class="text" style="font-weight: bold;">enterprise_processing_logs</text>
    <text x="240" y="175" class="text">log_id (PK) = Log_ID_2</text>
    <rect x="240" y="185" width="130" height="20" class="highlight"/>
    <text x="245" y="200" class="text">source_log_id (FK) = Log_ID_1</text>

    <!-- Linkage Arrow (ID Association) -->
    <line x="210" y="110" x2="230" y2="195" class="arrow" style="stroke-dasharray: 5,5; stroke-width: 2;"/>
    <text x="220" y="160" class="text" style="text-anchor: middle; font-style: italic;">主外键关联</text>
  </g>

  <!-- Index Optimization -->
  <rect x="650" y="100" width="130" height="130" class="table-box"/>
  <text x="660" y="120" class="text" style="font-weight: bold;">索引优化策略</text>
  <text x="660" y="145" class="text">B-Tree (等值查询)</text>
  <text x="660" y="170" class="text">GiST (地理空间)</text>
  <text x="660" y="195" class="text">BRIN (时序数据)</text>

  <line x="620" y="165" x2="650" y2="165" class="arrow"/>
</svg>