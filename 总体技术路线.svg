<?xml version="1.0"?>
<svg width="900" height="900" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
 <!-- 背景 -->
 <!-- 标题 -->
 <!-- 需求牵引层 -->
 <!-- 标准规范层 -->
 <!-- 平台架构层 -->
 <!-- 技术支撑层 -->
 <!-- 应用服务层 -->
 <!-- 持续优化 -->
 <!-- 箭头定义 -->
 <defs>
  <marker id="arrowhead" markerHeight="7" markerWidth="10" orient="auto" refX="10" refY="3.5">
   <polygon fill="black" id="svg_1" points="0 0, 10 3.5, 0 7"/>
  </marker>
 </defs>
 <!-- 层级连接线 - 使用折线 -->
 <!-- 反馈循环 - 使用折线 -->
 <!-- 说明文字 -->
 <g class="layer">
  <title>Layer 1</title>
  <text fill="black" font-size="20" font-weight="bold" id="svg_4" text-anchor="middle" x="450" y="30">时空数据安全监测平台总体技术路线</text>
  <g id="demand-layer">
   <rect fill="white" height="80" id="svg_5" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="55"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_6" text-anchor="middle" x="450" y="85">需求牵引层</text>
   <!-- 需求模块 -->
   <rect fill="#f0f0f0" height="30" id="svg_7" rx="3" stroke="black" stroke-width="1.5" width="150" x="100" y="95"/>
   <text fill="black" font-size="14" id="svg_8" text-anchor="middle" x="175" y="115">政府安全监管需求</text>
   <rect fill="#f0f0f0" height="30" id="svg_9" rx="3" stroke="black" stroke-width="1.5" width="150" x="280" y="95"/>
   <text fill="black" font-size="14" id="svg_10" text-anchor="middle" x="355" y="115">行业应用需求</text>
   <rect fill="#f0f0f0" height="30" id="svg_11" rx="3" stroke="black" stroke-width="1.5" width="150" x="460" y="95"/>
   <text fill="black" font-size="14" id="svg_12" text-anchor="middle" x="535" y="115">企业合规需求</text>
   <rect fill="#f0f0f0" height="30" id="svg_13" rx="3" stroke="black" stroke-width="1.5" width="150" x="640" y="95"/>
   <text fill="black" font-size="14" id="svg_14" text-anchor="middle" x="715" y="115">数据安全需求</text>
  </g>
  <g id="standard-layer">
   <rect fill="white" height="80" id="svg_15" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="163"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_16" text-anchor="middle" x="450" y="193">标准规范体系</text>
   <rect fill="#e0e0e0" height="30" id="svg_17" rx="3" stroke="black" stroke-width="1.5" width="120" x="100" y="203"/>
   <text fill="black" font-size="13" id="svg_18" text-anchor="middle" x="160" y="223">数据分类分级</text>
   <rect fill="#e0e0e0" height="30" id="svg_19" rx="3" stroke="black" stroke-width="1.5" width="120" x="240" y="203"/>
   <text fill="black" font-size="13" id="svg_20" text-anchor="middle" x="300" y="223">安全处理规范</text>
   <rect fill="#e0e0e0" height="30" id="svg_21" rx="3" stroke="black" stroke-width="1.5" width="120" x="380" y="203"/>
   <text fill="black" font-size="13" id="svg_22" text-anchor="middle" x="440" y="223">通信协议标准</text>
   <rect fill="#e0e0e0" height="30" id="svg_23" rx="3" stroke="black" stroke-width="1.5" width="120" x="520" y="203"/>
   <text fill="black" font-size="13" id="svg_24" text-anchor="middle" x="580" y="223">监测预警规范</text>
   <rect fill="#e0e0e0" height="30" id="svg_25" rx="3" stroke="black" stroke-width="1.5" width="120" x="660" y="203"/>
   <text fill="black" font-size="13" id="svg_26" text-anchor="middle" x="720" y="223">接口规范标准</text>
  </g>
  <g id="platform-layer">
   <rect fill="white" height="120" id="svg_27" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="271"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_28" text-anchor="middle" x="450" y="301">四级监管架构体系</text>
   <!-- 四级架构 -->
   <rect fill="#d0d0d0" height="50" id="svg_29" rx="3" stroke="black" stroke-width="1.5" width="160" x="100" y="321"/>
   <text fill="black" font-size="14" id="svg_30" text-anchor="middle" x="180" y="351">国家监测平台</text>
   <rect fill="#d0d0d0" height="50" id="svg_31" rx="3" stroke="black" stroke-width="1.5" width="160" x="290" y="321"/>
   <text fill="black" font-size="14" id="svg_32" text-anchor="middle" x="370" y="351">属地监测平台</text>
   <rect fill="#d0d0d0" height="50" id="svg_33" rx="3" stroke="black" stroke-width="1.5" width="160" x="480" y="321"/>
   <text fill="black" font-size="14" id="svg_34" text-anchor="middle" x="560" y="351">企业平台</text>
   <rect fill="#d0d0d0" height="50" id="svg_35" rx="3" stroke="black" stroke-width="1.5" width="130" x="670" y="321"/>
   <text fill="black" font-size="14" id="svg_36" text-anchor="middle" x="735" y="351">终端节点</text>
   <!-- 连接线 - 使用折线 -->
   <polyline fill="none" id="svg_37" marker-end="url(#arrowhead)" points="260,346 275,346 275,346 290,346 " stroke="black" stroke-width="2"/>
   <polyline fill="none" id="svg_38" marker-end="url(#arrowhead)" points="450,346 465,346 465,346 480,346 " stroke="black" stroke-width="2"/>
   <polyline fill="none" id="svg_39" marker-end="url(#arrowhead)" points="640,346 655,346 655,346 670,346 " stroke="black" stroke-width="2"/>
  </g>
  <g id="tech-layer">
   <rect fill="white" height="120" id="svg_40" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="416"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_41" text-anchor="middle" x="450" y="446">核心技术支撑</text>
   <!-- 技术模块 - 第一行 -->
   <rect fill="#f0f0f0" height="30" id="svg_42" rx="3" stroke="black" stroke-width="1.5" width="140" x="80" y="466"/>
   <text fill="black" font-size="12" id="svg_43" text-anchor="middle" x="150" y="486">风险识别与预警</text>
   <rect fill="#f0f0f0" height="30" id="svg_44" rx="3" stroke="black" stroke-width="1.5" width="140" x="240" y="466"/>
   <text fill="black" font-size="12" id="svg_45" text-anchor="middle" x="310" y="486">数据溯源追踪</text>
   <rect fill="#f0f0f0" height="30" id="svg_46" rx="3" stroke="black" stroke-width="1.5" width="140" x="400" y="466"/>
   <text fill="black" font-size="12" id="svg_47" text-anchor="middle" x="470" y="486">区块链存证</text>
   <rect fill="#f0f0f0" height="30" id="svg_48" rx="3" stroke="black" stroke-width="1.5" width="140" x="560" y="466"/>
   <text fill="black" font-size="12" id="svg_49" text-anchor="middle" x="630" y="486">实时流处理</text>
   <rect fill="#f0f0f0" height="30" id="svg_50" rx="3" stroke="black" stroke-width="1.5" width="110" x="720" y="466"/>
   <text fill="black" font-size="12" id="svg_51" text-anchor="middle" x="775" y="486">AI智能分析</text>
   <!-- 技术基础 -->
   <rect fill="#e0e0e0" height="20" id="svg_52" rx="3" stroke="black" width="500" x="200" y="506"/>
   <text fill="black" font-size="12" id="svg_53" text-anchor="middle" x="450" y="520">分布式架构 | 微服务 | 容器化 | PB级存储 | 国密算法</text>
  </g>
  <g id="service-layer">
   <rect fill="white" height="80" id="svg_54" rx="5" stroke="black" stroke-width="2" width="800" x="51" y="564"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_55" text-anchor="middle" x="451" y="594">应用服务创新</text>
   <rect fill="#d0d0d0" height="30" id="svg_56" rx="3" stroke="black" stroke-width="1.5" width="120" x="101" y="604"/>
   <text fill="black" font-size="13" id="svg_57" text-anchor="middle" x="161" y="624">安全预警</text>
   <rect fill="#d0d0d0" height="30" id="svg_58" rx="3" stroke="black" stroke-width="1.5" width="120" x="241" y="604"/>
   <text fill="black" font-size="13" id="svg_59" text-anchor="middle" x="301" y="624">监管决策</text>
   <rect fill="#d0d0d0" height="30" id="svg_60" rx="3" stroke="black" stroke-width="1.5" width="120" x="381" y="604"/>
   <text fill="black" font-size="13" id="svg_61" text-anchor="middle" x="441" y="624">合规评估</text>
   <rect fill="#d0d0d0" height="30" id="svg_62" rx="3" stroke="black" stroke-width="1.5" width="120" x="521" y="604"/>
   <text fill="black" font-size="13" id="svg_63" text-anchor="middle" x="581" y="624">应急响应</text>
   <rect fill="#d0d0d0" height="30" id="svg_64" rx="3" stroke="black" stroke-width="1.5" width="120" x="661" y="604"/>
   <text fill="black" font-size="13" id="svg_65" text-anchor="middle" x="721" y="624">数据共享</text>
  </g>
  <g id="optimize">
   <rect fill="white" height="40" id="svg_66" rx="5" stroke="black" stroke-dasharray="5,5" stroke-width="2" width="300" x="300" y="685"/>
   <text fill="black" font-size="14" id="svg_67" text-anchor="middle" x="450" y="710">持续优化迭代</text>
  </g>
  <polyline fill="none" id="svg_73" marker-end="url(#arrowhead)" points="299,710.0000095367432 21,710.0000095367432 21,405.0000047683716 21,100 46.272727966308594,100 " stroke="black" stroke-dasharray="3,3" stroke-width="1.5"/>
  <text fill="black" font-size="12" id="svg_74" transform="rotate(-90 34 371.25)" x="10" y="376">反馈优化</text>
  <g id="svg_78">
   <line fill="none" id="svg_76" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 446.544 657.665)" x1="436.348897" x2="456.738722" y1="657.665237" y2="657.665237"/>
   <path d="m441.326947,679.614333l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_77" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 446.544 674.129)"/>
  </g>
  <g id="svg_79">
   <line fill="none" id="svg_80" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 446.892 543.558)" x1="440.652101" x2="453.132323" y1="543.558149" y2="543.558149"/>
   <path d="m441.675322,561.552472l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_81" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 446.892 556.067)"/>
  </g>
  <g id="svg_82">
   <line fill="none" id="svg_83" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 447.458 398.36)" x1="441.217852" x2="453.698074" y1="398.360322" y2="398.360322"/>
   <path d="m442.241073,416.354645l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_84" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 447.458 410.869)"/>
  </g>
  <g id="svg_85">
   <line fill="none" id="svg_86" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 447.457 251.468)" x1="441.217319" x2="453.697541" y1="251.468072" y2="251.468072"/>
   <path d="m442.24054,269.462395l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_87" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 447.457 263.977)"/>
  </g>
  <g id="svg_88">
   <line fill="none" id="svg_89" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 447.458 142.993)" x1="441.218281" x2="453.698503" y1="142.992887" y2="142.992887"/>
   <path d="m442.241502,160.98721l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_90" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 447.458 155.502)"/>
  </g>
 </g>
</svg>