<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能数据溯源追踪系统 - 3D可视化演示</title>
    <script src="assets/js/d3.v7.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            overflow: hidden;
            color: white;
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .header {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 1000;
        }

        .title {
            font-size: 24px;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 5px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .subtitle {
            font-size: 14px;
            color: #8892b0;
            opacity: 0.8;
        }

        .controls {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #00d4ff;
            border-radius: 20px;
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            background: rgba(0, 212, 255, 0.2);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        #visualization {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .node-group {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node-group:hover {
            filter: brightness(1.3);
        }

        .node-circle {
            filter: drop-shadow(0 0 8px currentColor);
            transition: all 0.3s ease;
        }

        .node-text {
            font-family: 'SF Pro Display', sans-serif;
            font-size: 11px;
            font-weight: 600;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: white;
            text-shadow: 0 0 3px rgba(0,0,0,0.8);
            pointer-events: none;
        }

        .node-label {
            font-family: 'SF Mono', monospace;
            font-size: 9px;
            text-anchor: middle;
            fill: #8892b0;
            pointer-events: none;
        }

        .node-status {
            font-size: 8px;
            text-anchor: middle;
            fill: #00ff88;
            pointer-events: none;
        }

        .link-path {
            fill: none;
            stroke-width: 2;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .link-path:hover {
            opacity: 1;
            stroke-width: 3;
        }

        .data-flow {
            stroke-dasharray: 8,4;
            animation: flowAnimation 2s linear infinite;
        }

        .highlight-path {
            stroke: #ff6b35;
            stroke-width: 3;
            opacity: 0.9;
            filter: drop-shadow(0 0 5px #ff6b35);
        }

        @keyframes flowAnimation {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -12; }
        }

        .pulse {
            animation: pulseAnimation 2s infinite;
        }

        @keyframes pulseAnimation {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        .sphere-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 800px;
            height: 600px;
            perspective: 1200px;
            z-index: 100;
            display: none;
        }

        .business-sphere {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
            animation: businessRotate 60s linear infinite;
        }

        @keyframes businessRotate {
            0% { transform: rotateX(15deg) rotateY(0deg) rotateZ(0deg); }
            100% { transform: rotateX(15deg) rotateY(360deg) rotateZ(0deg); }
        }

        .data-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            opacity: 0.9;
        }

        /* 球体层级定位 */
        .sphere-layer-1 { transform: translateZ(250px) rotateX(0deg); }
        .sphere-layer-2 { transform: translateZ(150px) rotateX(20deg); }
        .sphere-layer-3 { transform: translateZ(0px) rotateX(0deg); }
        .sphere-layer-4 { transform: translateZ(-150px) rotateX(-20deg); }
        .sphere-layer-5 { transform: translateZ(-250px) rotateX(0deg); }

        .layer-title {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #00d4ff;
            font-size: 14px;
            font-weight: 600;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            z-index: 10;
        }

        .business-node {
            position: absolute;
            width: 80px;
            height: 80px;
            background: rgba(0, 20, 40, 0.8);
            border: 2px solid #00d4ff;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }

        .business-node:hover {
            transform: scale(1.2);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
            border-color: #00ff88;
        }

        .node-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .node-label {
            font-size: 10px;
            color: white;
            text-align: center;
            font-weight: 500;
        }

        .vehicle-node { border-color: #ff6b35; box-shadow: 0 0 20px rgba(255, 107, 53, 0.3); }
        .sensor-node { border-color: #4f46e5; box-shadow: 0 0 20px rgba(79, 70, 229, 0.3); }
        .gateway-node { border-color: #059669; box-shadow: 0 0 20px rgba(5, 150, 105, 0.3); }
        .etl-node { border-color: #10b981; box-shadow: 0 0 20px rgba(16, 185, 129, 0.3); }
        .storage-node { border-color: #dc2626; box-shadow: 0 0 20px rgba(220, 38, 38, 0.3); }
        .ai-node { border-color: #fbbf24; box-shadow: 0 0 20px rgba(251, 191, 36, 0.3); }
        .api-node { border-color: #8b5cf6; box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); }
        .blockchain-node { border-color: #7c3aed; box-shadow: 0 0 20px rgba(124, 58, 237, 0.3); }
        .enterprise-node { border-color: #06b6d4; box-shadow: 0 0 20px rgba(6, 182, 212, 0.3); }

        .sphere-connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            transform-style: preserve-3d;
        }

        .connection-line-3d {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            transform-origin: left center;
            opacity: 0.8;
            animation: dataFlow3D 3s linear infinite;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        @keyframes dataFlow3D {
            0% {
                opacity: 0.3;
                box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
            }
            50% {
                opacity: 1;
                box-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
            }
            100% {
                opacity: 0.3;
                box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
            }
        }

        /* 2D视图拖拽支持 */
        #visualization svg {
            cursor: grab;
        }

        #visualization svg:active {
            cursor: grabbing;
        }

        .link-path {
            fill: none;
            stroke-width: 2;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .link-path.straight {
            stroke-dasharray: none;
        }

        .sphere-ring {
            position: absolute;
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 50%;
            animation: ringPulse 4s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3), inset 0 0 20px rgba(0, 212, 255, 0.1);
        }

        @keyframes ringPulse {
            0%, 100% {
                opacity: 0.4;
                transform: scale(1) rotateX(0deg);
                border-color: rgba(0, 212, 255, 0.4);
            }
            25% {
                opacity: 0.8;
                transform: scale(1.05) rotateX(90deg);
                border-color: rgba(0, 255, 136, 0.6);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.1) rotateX(180deg);
                border-color: rgba(255, 107, 53, 0.5);
            }
            75% {
                opacity: 0.9;
                transform: scale(1.05) rotateX(270deg);
                border-color: rgba(124, 58, 237, 0.6);
            }
        }

        .data-particle {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            animation: particleFloat 6s ease-in-out infinite;
        }

        .particle-type-1 {
            background: #00ff88;
            box-shadow: 0 0 12px #00ff88, 0 0 24px rgba(0, 255, 136, 0.3);
        }

        .particle-type-2 {
            background: #00d4ff;
            box-shadow: 0 0 12px #00d4ff, 0 0 24px rgba(0, 212, 255, 0.3);
        }

        .particle-type-3 {
            background: #ff6b35;
            box-shadow: 0 0 12px #ff6b35, 0 0 24px rgba(255, 107, 53, 0.3);
        }

        .particle-type-4 {
            background: #7c3aed;
            box-shadow: 0 0 12px #7c3aed, 0 0 24px rgba(124, 58, 237, 0.3);
        }

        @keyframes particleFloat {
            0% {
                transform: translateZ(0px) translateX(0px) translateY(0px);
                opacity: 1;
            }
            25% {
                transform: translateZ(80px) translateX(20px) translateY(-20px);
                opacity: 0.8;
            }
            50% {
                transform: translateZ(120px) translateX(-15px) translateY(25px);
                opacity: 0.4;
            }
            75% {
                transform: translateZ(80px) translateX(-25px) translateY(-10px);
                opacity: 0.7;
            }
            100% {
                transform: translateZ(0px) translateX(0px) translateY(0px);
                opacity: 1;
            }
        }

        .metrics-panel {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 20, 40, 0.8);
            border: 1px solid #00d4ff;
            border-radius: 10px;
            padding: 15px;
            min-width: 200px;
            backdrop-filter: blur(10px);
        }

        .metrics-title {
            font-size: 14px;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 10px;
            text-align: center;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 11px;
        }

        .metric-label {
            color: #8892b0;
        }

        .metric-value {
            color: #00ff88;
            font-weight: 600;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 20, 40, 0.95);
            color: #00d4ff;
            padding: 10px;
            border-radius: 8px;
            font-size: 11px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 2000;
            border: 1px solid #00d4ff;
            backdrop-filter: blur(10px);
        }

        .connection-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            opacity: 0.6;
            animation: connectionPulse 3s ease-in-out infinite;
        }

        @keyframes connectionPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #00ff88;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: statusBlink 2s infinite;
        }

        @keyframes statusBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 智能数据溯源追踪系统</h1>
            <p class="subtitle">3D Data Lineage Visualization Platform</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="startDataFlow()">🚀 启动数据流</button>
            <button class="btn" onclick="highlightTraceability()">🔍 溯源路径</button>
            <button class="btn" onclick="toggle3DView()">🎯 3D视图</button>
            <button class="btn" onclick="resetView()">🔄 重置</button>
        </div>

        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>系统运行中</span>
        </div>

        <div id="visualization">
            <!-- 3D业务数据溯源球体 -->
            <div class="sphere-container" id="sphereContainer">
                <div class="business-sphere" id="businessSphere">
                    <!-- 数据源层 (最外层) -->
                    <div class="data-layer sphere-layer-1" id="sourceLayer">
                        <div class="layer-title">数据源层</div>
                        <div class="business-node vehicle-node" data-business="车载终端" data-node-id="vehicle" style="left: 200px; top: 150px;">
                            <div class="node-icon">🚗</div>
                            <div class="node-label">车载终端</div>
                        </div>
                        <div class="business-node sensor-node" data-business="传感器" data-node-id="sensor" style="left: 500px; top: 200px;">
                            <div class="node-icon">📡</div>
                            <div class="node-label">传感器</div>
                        </div>
                    </div>

                    <!-- 采集处理层 -->
                    <div class="data-layer sphere-layer-2" id="processLayer">
                        <div class="layer-title">采集处理层</div>
                        <div class="business-node gateway-node" data-business="数据网关" data-node-id="gateway" style="left: 150px; top: 250px;">
                            <div class="node-icon">🌐</div>
                            <div class="node-label">数据网关</div>
                        </div>
                        <div class="business-node etl-node" data-business="ETL处理" data-node-id="etl" style="left: 550px; top: 180px;">
                            <div class="node-icon">⚙️</div>
                            <div class="node-label">ETL处理</div>
                        </div>
                    </div>

                    <!-- 存储分析层 (中心层) -->
                    <div class="data-layer sphere-layer-3" id="storageLayer">
                        <div class="layer-title">存储分析层</div>
                        <div class="business-node storage-node" data-business="数据湖" data-node-id="storage" style="left: 300px; top: 300px;">
                            <div class="node-icon">🗄️</div>
                            <div class="node-label">数据湖</div>
                        </div>
                        <div class="business-node ai-node" data-business="AI分析" data-node-id="ai" style="left: 450px; top: 150px;">
                            <div class="node-icon">🤖</div>
                            <div class="node-label">AI分析</div>
                        </div>
                    </div>

                    <!-- 应用服务层 -->
                    <div class="data-layer sphere-layer-4" id="serviceLayer">
                        <div class="layer-title">应用服务层</div>
                        <div class="business-node api-node" data-business="API服务" data-node-id="api" style="left: 250px; top: 350px;">
                            <div class="node-icon">🔗</div>
                            <div class="node-label">API服务</div>
                        </div>
                        <div class="business-node blockchain-node" data-business="区块链" data-node-id="blockchain" style="left: 500px; top: 300px;">
                            <div class="node-icon">⛓️</div>
                            <div class="node-label">区块链</div>
                        </div>
                    </div>

                    <!-- 业务应用层 (最内层) -->
                    <div class="data-layer sphere-layer-5" id="applicationLayer">
                        <div class="layer-title">业务应用层</div>
                        <div class="business-node enterprise-node" data-business="企业平台" data-node-id="enterprise" style="left: 375px; top: 275px;">
                            <div class="node-icon">🏢</div>
                            <div class="node-label">企业平台</div>
                        </div>
                    </div>

                    <!-- 3D连线容器 -->
                    <div class="sphere-connections" id="sphereConnections"></div>
                </div>
            </div>
        </div>

        <div class="metrics-panel">
            <div class="metrics-title">📊 实时监控</div>
            <div class="metric-item">
                <span class="metric-label">数据吞吐:</span>
                <span class="metric-value" id="throughput">2.3 GB/s</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">响应延迟:</span>
                <span class="metric-value" id="latency">8ms</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">成功率:</span>
                <span class="metric-value" id="successRate">99.97%</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">活跃节点:</span>
                <span class="metric-value" id="activeNodes">11</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">溯源深度:</span>
                <span class="metric-value" id="traceDepth">5层</span>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 数据定义
        const nodes = [
            { id: 'edge', name: 'Edge IoT', label: '车载传感器', x: 200, y: 300, color: '#ff6b35', size: 30, status: 'active', angle: 0 },
            { id: 'cloud', name: 'Cloud Storage', label: '分布式存储', x: 350, y: 200, color: '#4f46e5', size: 35, status: 'active', angle: 45 },
            { id: 'backup', name: 'Backup', label: '备份存储', x: 350, y: 400, color: '#6366f1', size: 25, status: 'normal', angle: 90 },
            { id: 'ml', name: 'ML Model', label: '模型训练', x: 500, y: 150, color: '#059669', size: 28, status: 'normal', angle: 135 },
            { id: 'ai', name: 'AI Engine', label: '深度学习处理', x: 600, y: 300, color: '#10b981', size: 40, status: 'selected', gpu: '87%', angle: 180 },
            { id: 'feature', name: 'Feature', label: '特征工程', x: 500, y: 450, color: '#34d399', size: 25, status: 'normal', angle: 225 },
            { id: 'gateway', name: 'Gateway', label: 'API网关', x: 750, y: 200, color: '#dc2626', size: 22, status: 'normal', angle: 270 },
            { id: 'api', name: 'Secure API', label: '加密传输', x: 850, y: 300, color: '#ef4444', size: 32, status: 'active', security: 'TLS 1.3', angle: 315 },
            { id: 'monitor', name: 'Monitor', label: '监控中心', x: 750, y: 400, color: '#f87171', size: 20, status: 'normal', angle: 360 },
            { id: 'blockchain', name: 'Blockchain', label: '存证链', x: 1000, y: 350, color: '#7c3aed', size: 30, status: 'active', block: '#1247', angle: 45 },
            { id: 'enterprise', name: 'Enterprise', label: '企业接收方', x: 1150, y: 300, color: '#8b5cf6', size: 35, status: 'online', angle: 90 }
        ];

        const links = [
            { source: 'edge', target: 'cloud', type: 'main', strength: 0.8 },
            { source: 'edge', target: 'backup', type: 'backup', strength: 0.3 },
            { source: 'cloud', target: 'ai', type: 'main', strength: 0.9 },
            { source: 'cloud', target: 'ml', type: 'normal', strength: 0.5 },
            { source: 'backup', target: 'feature', type: 'normal', strength: 0.4 },
            { source: 'ai', target: 'api', type: 'main', strength: 0.9 },
            { source: 'ai', target: 'gateway', type: 'normal', strength: 0.6 },
            { source: 'feature', target: 'monitor', type: 'normal', strength: 0.4 },
            { source: 'api', target: 'enterprise', type: 'main', strength: 0.8 },
            { source: 'api', target: 'blockchain', type: 'security', strength: 0.7 },
            { source: 'blockchain', target: 'enterprise', type: 'main', strength: 0.8 }
        ];

        // 创建SVG
        const width = window.innerWidth;
        const height = window.innerHeight;
        const svg = d3.select('#visualization')
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .style('position', 'absolute')
            .style('top', 0)
            .style('left', 0);

        // 添加拖拽和缩放功能
        const zoom = d3.zoom()
            .scaleExtent([0.5, 3])
            .on('zoom', function(event) {
                const { transform } = event;
                nodeGroup.attr('transform', transform);
                linkGroup.attr('transform', transform);
            });

        svg.call(zoom);

        // 创建渐变和滤镜
        const defs = svg.append('defs');

        // 发光滤镜
        const glowFilter = defs.append('filter')
            .attr('id', 'glow')
            .attr('x', '-50%')
            .attr('y', '-50%')
            .attr('width', '200%')
            .attr('height', '200%');

        glowFilter.append('feGaussianBlur')
            .attr('stdDeviation', '4')
            .attr('result', 'coloredBlur');

        const feMerge = glowFilter.append('feMerge');
        feMerge.append('feMergeNode').attr('in', 'coloredBlur');
        feMerge.append('feMergeNode').attr('in', 'SourceGraphic');

        // 箭头标记
        const arrowMarker = defs.append('marker')
            .attr('id', 'arrowhead')
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', 8)
            .attr('refY', 0)
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('orient', 'auto');

        arrowMarker.append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('fill', '#00d4ff');

        // 创建连线组
        const linkGroup = svg.append('g').attr('class', 'links');
        const nodeGroup = svg.append('g').attr('class', 'nodes');

        // 绘制连线
        const linkElements = linkGroup.selectAll('.link-path')
            .data(links)
            .enter()
            .append('line')
            .attr('class', 'link-path straight')
            .attr('stroke', d => {
                switch(d.type) {
                    case 'main': return '#00d4ff';
                    case 'security': return '#ff6b35';
                    case 'backup': return '#6366f1';
                    default: return '#8892b0';
                }
            })
            .attr('stroke-width', d => 2 + d.strength * 2)
            .attr('marker-end', 'url(#arrowhead)')
            .attr('x1', d => {
                const source = nodes.find(n => n.id === d.source);
                return source.x;
            })
            .attr('y1', d => {
                const source = nodes.find(n => n.id === d.source);
                return source.y;
            })
            .attr('x2', d => {
                const target = nodes.find(n => n.id === d.target);
                return target.x;
            })
            .attr('y2', d => {
                const target = nodes.find(n => n.id === d.target);
                return target.y;
            });

        // 绘制节点
        const nodeElements = nodeGroup.selectAll('.node-group')
            .data(nodes)
            .enter()
            .append('g')
            .attr('class', 'node-group')
            .attr('transform', d => `translate(${d.x}, ${d.y})`);

        // 节点外圈（选中状态）
        nodeElements.filter(d => d.status === 'selected')
            .append('circle')
            .attr('r', d => d.size + 8)
            .attr('fill', 'none')
            .attr('stroke', '#fbbf24')
            .attr('stroke-width', 2)
            .attr('class', 'pulse');

        // 节点主体
        nodeElements.append('circle')
            .attr('class', 'node-circle')
            .attr('r', d => d.size)
            .attr('fill', d => d.color)
            .attr('filter', 'url(#glow)')
            .style('color', d => d.color);

        // 节点文字
        nodeElements.append('text')
            .attr('class', 'node-text')
            .attr('y', 0)
            .text(d => d.name);

        // 节点标签
        nodeElements.append('text')
            .attr('class', 'node-label')
            .attr('y', d => d.size + 18)
            .text(d => d.label);

        // 状态指示器
        nodeElements.filter(d => d.gpu)
            .append('text')
            .attr('class', 'node-status')
            .attr('y', d => -d.size - 8)
            .text(d => `GPU: ${d.gpu}`);

        nodeElements.filter(d => d.security)
            .append('text')
            .attr('class', 'node-status')
            .attr('y', d => -d.size - 8)
            .text(d => d.security);

        nodeElements.filter(d => d.block)
            .append('text')
            .attr('class', 'node-status')
            .attr('y', d => -d.size - 8)
            .text(d => `Block ${d.block}`);

        // 活跃状态指示点
        nodeElements.filter(d => d.status === 'active' || d.status === 'online')
            .append('circle')
            .attr('cx', d => d.size - 8)
            .attr('cy', d => -d.size + 8)
            .attr('r', 4)
            .attr('fill', '#00ff88')
            .attr('class', 'pulse');

        // 工具提示
        const tooltip = d3.select('#tooltip');

        nodeElements
            .on('mouseover', function(event, d) {
                tooltip
                    .style('opacity', 1)
                    .style('left', (event.pageX + 10) + 'px')
                    .style('top', (event.pageY - 10) + 'px')
                    .html(`
                        <strong>${d.name}</strong><br/>
                        类型: ${d.label}<br/>
                        状态: ${d.status}<br/>
                        ${d.gpu ? `GPU利用率: ${d.gpu}<br/>` : ''}
                        ${d.security ? `安全协议: ${d.security}<br/>` : ''}
                        ${d.block ? `区块高度: ${d.block}<br/>` : ''}
                    `);
            })
            .on('mouseout', function() {
                tooltip.style('opacity', 0);
            });

        // 添加数据粒子到3D球体
        function addDataParticles() {
            const sphereContainer = document.getElementById('dataSphere');
            const particleTypes = ['particle-type-1', 'particle-type-2', 'particle-type-3', 'particle-type-4'];

            for (let i = 0; i < 40; i++) {
                const particle = document.createElement('div');
                const typeClass = particleTypes[Math.floor(Math.random() * particleTypes.length)];
                particle.className = `data-particle ${typeClass}`;

                // 在球体范围内随机分布
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 120 + 20;
                const x = Math.cos(angle) * radius + 150;
                const y = Math.sin(angle) * radius + 150;

                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (4 + Math.random() * 4) + 's';

                sphereContainer.appendChild(particle);
            }

            // 添加中心核心粒子
            const coreParticle = document.createElement('div');
            coreParticle.className = 'data-particle particle-type-2';
            coreParticle.style.left = '147px';
            coreParticle.style.top = '147px';
            coreParticle.style.width = '12px';
            coreParticle.style.height = '12px';
            coreParticle.style.animationDuration = '3s';
            coreParticle.style.zIndex = '200';
            sphereContainer.appendChild(coreParticle);
        }

        // 功能函数
        let animationActive = false;

        function startDataFlow() {
            if (animationActive) return;
            animationActive = true;

            linkElements
                .filter(d => d.type === 'main')
                .classed('data-flow', true);

            setTimeout(() => {
                linkElements.classed('data-flow', false);
                animationActive = false;
            }, 5000);
        }

        function highlightTraceability() {
            const mainPath = links.filter(d => d.type === 'main');
            linkElements
                .filter(d => d.type === 'main')
                .classed('highlight-path', true);

            setTimeout(() => {
                linkElements.classed('highlight-path', false);
            }, 4000);
        }

        let is3DView = false;

        function toggle3DView() {
            const sphereContainer = document.getElementById('sphereContainer');
            const svgVisualization = svg.node();

            if (!is3DView) {
                // 切换到3D业务视图
                sphereContainer.style.display = 'block';
                svgVisualization.style.display = 'none';
                is3DView = true;

                // 启动3D业务溯源动画
                setTimeout(() => {
                    startBusinessTraceAnimation();
                }, 500);

                // 更新按钮文字
                document.querySelector('[onclick="toggle3DView()"]').textContent = '📊 2D视图';
            } else {
                // 切换回2D网络视图
                sphereContainer.style.display = 'none';
                svgVisualization.style.display = 'block';
                is3DView = false;

                // 更新按钮文字
                document.querySelector('[onclick="toggle3DView()"]').textContent = '🎯 3D视图';
            }
        }

        function startBusinessTraceAnimation() {
            const connectionsContainer = document.getElementById('sphereConnections');

            // 清除现有连线
            connectionsContainer.innerHTML = '';

            // 定义3D业务数据流连接
            const businessConnections = [
                { from: 'vehicle', to: 'gateway', color: '#ff6b35', delay: 0 },
                { from: 'sensor', to: 'etl', color: '#4f46e5', delay: 500 },
                { from: 'gateway', to: 'storage', color: '#059669', delay: 1000 },
                { from: 'etl', to: 'ai', color: '#10b981', delay: 1500 },
                { from: 'storage', to: 'api', color: '#dc2626', delay: 2000 },
                { from: 'ai', to: 'blockchain', color: '#fbbf24', delay: 2500 },
                { from: 'api', to: 'enterprise', color: '#8b5cf6', delay: 3000 },
                { from: 'blockchain', to: 'enterprise', color: '#7c3aed', delay: 3500 }
            ];

            // 创建3D连线
            businessConnections.forEach((connection, index) => {
                setTimeout(() => {
                    create3DConnection(connection.from, connection.to, connection.color);
                }, connection.delay);
            });
        }

        function create3DConnection(fromNodeId, toNodeId, color) {
            const fromNode = document.querySelector(`[data-node-id="${fromNodeId}"]`);
            const toNode = document.querySelector(`[data-node-id="${toNodeId}"]`);

            if (!fromNode || !toNode) return;

            // 获取节点位置
            const fromRect = fromNode.getBoundingClientRect();
            const toRect = toNode.getBoundingClientRect();
            const containerRect = document.getElementById('sphereConnections').getBoundingClientRect();

            const fromX = fromRect.left - containerRect.left + fromRect.width / 2;
            const fromY = fromRect.top - containerRect.top + fromRect.height / 2;
            const toX = toRect.left - containerRect.left + toRect.width / 2;
            const toY = toRect.top - containerRect.top + toRect.height / 2;

            // 计算连线长度和角度
            const dx = toX - fromX;
            const dy = toY - fromY;
            const length = Math.sqrt(dx * dx + dy * dy);
            const angle = Math.atan2(dy, dx) * 180 / Math.PI;

            // 创建连线元素
            const connectionLine = document.createElement('div');
            connectionLine.className = 'connection-line-3d';
            connectionLine.style.left = fromX + 'px';
            connectionLine.style.top = fromY + 'px';
            connectionLine.style.width = length + 'px';
            connectionLine.style.transform = `rotate(${angle}deg)`;
            connectionLine.style.background = `linear-gradient(90deg, transparent, ${color}, transparent)`;
            connectionLine.style.boxShadow = `0 0 10px ${color}`;
            connectionLine.style.animationDelay = Math.random() * 2 + 's';

            document.getElementById('sphereConnections').appendChild(connectionLine);
        }

        function resetView() {
            linkElements
                .classed('highlight-path', false)
                .classed('data-flow', false);
            animationActive = false;
        }

        // 实时更新指标
        function updateMetrics() {
            document.getElementById('throughput').textContent = (2.1 + Math.random() * 0.4).toFixed(1) + ' GB/s';
            document.getElementById('latency').textContent = Math.floor(6 + Math.random() * 4) + 'ms';
            document.getElementById('successRate').textContent = (99.95 + Math.random() * 0.04).toFixed(2) + '%';
            document.getElementById('traceDepth').textContent = Math.floor(4 + Math.random() * 3) + '层';
        }

        // 业务节点交互
        function initBusinessNodeInteractions() {
            const businessNodes = document.querySelectorAll('.business-node');
            const tooltip = document.getElementById('tooltip');

            businessNodes.forEach(node => {
                node.addEventListener('mouseenter', function(e) {
                    const business = this.getAttribute('data-business');
                    const layer = this.closest('.data-layer').querySelector('.layer-title').textContent;

                    tooltip.style.opacity = '1';
                    tooltip.style.left = (e.pageX + 10) + 'px';
                    tooltip.style.top = (e.pageY - 10) + 'px';
                    tooltip.innerHTML = `
                        <strong>${business}</strong><br/>
                        所属层级: ${layer}<br/>
                        状态: 运行中<br/>
                        数据流量: ${(Math.random() * 100).toFixed(1)} MB/s<br/>
                        处理延迟: ${Math.floor(Math.random() * 50 + 10)}ms
                    `;
                });

                node.addEventListener('mouseleave', function() {
                    tooltip.style.opacity = '0';
                });

                node.addEventListener('click', function() {
                    // 高亮该节点相关的数据流路径
                    const business = this.getAttribute('data-business');
                    highlightBusinessPath(business);
                });
            });
        }

        function highlightBusinessPath(businessType) {
            // 根据业务类型高亮相关路径
            const paths = document.querySelectorAll('.trace-path');
            paths.forEach(path => {
                path.style.opacity = '0.3';
            });

            // 这里可以根据具体业务逻辑来高亮特定路径
            setTimeout(() => {
                paths.forEach(path => {
                    path.style.opacity = '0.7';
                });
            }, 2000);
        }

        // 初始化
        addDataParticles();
        initBusinessNodeInteractions();
        setInterval(updateMetrics, 2000);

        // 自动启动演示
        setTimeout(() => {
            startDataFlow();
        }, 1500);
    </script>
</body>
</html>
