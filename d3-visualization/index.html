<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能数据溯源追踪系统 - 3D可视化演示</title>
    <script src="assets/js/d3.v7.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            overflow: hidden;
            color: white;
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .header {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 1000;
        }

        .title {
            font-size: 24px;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 5px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .subtitle {
            font-size: 14px;
            color: #8892b0;
            opacity: 0.8;
        }

        .controls {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #00d4ff;
            border-radius: 20px;
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            background: rgba(0, 212, 255, 0.2);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        #visualization {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .node-group {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node-group:hover {
            filter: brightness(1.3);
        }

        .node-circle {
            filter: drop-shadow(0 0 8px currentColor);
            transition: all 0.3s ease;
        }

        .node-text {
            font-family: 'SF Pro Display', sans-serif;
            font-size: 11px;
            font-weight: 600;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: white;
            text-shadow: 0 0 3px rgba(0,0,0,0.8);
            pointer-events: none;
        }

        .node-label {
            font-family: 'SF Mono', monospace;
            font-size: 9px;
            text-anchor: middle;
            fill: #8892b0;
            pointer-events: none;
        }

        .node-status {
            font-size: 8px;
            text-anchor: middle;
            fill: #00ff88;
            pointer-events: none;
        }

        .link-path {
            fill: none;
            stroke-width: 2;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .link-path:hover {
            opacity: 1;
            stroke-width: 3;
        }

        .data-flow {
            stroke-dasharray: 8,4;
            animation: flowAnimation 2s linear infinite;
        }

        .highlight-path {
            stroke: #ff6b35;
            stroke-width: 3;
            opacity: 0.9;
            filter: drop-shadow(0 0 5px #ff6b35);
        }

        @keyframes flowAnimation {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -12; }
        }

        .pulse {
            animation: pulseAnimation 2s infinite;
        }

        @keyframes pulseAnimation {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        .sphere-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            perspective: 1200px;
            z-index: 100;
        }

        .data-sphere {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
            animation: rotate3D 30s linear infinite;
        }

        @keyframes rotate3D {
            0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
            33% { transform: rotateX(120deg) rotateY(120deg) rotateZ(120deg); }
            66% { transform: rotateX(240deg) rotateY(240deg) rotateZ(240deg); }
            100% { transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg); }
        }

        .sphere-ring {
            position: absolute;
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 50%;
            animation: ringPulse 4s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3), inset 0 0 20px rgba(0, 212, 255, 0.1);
        }

        @keyframes ringPulse {
            0%, 100% {
                opacity: 0.4;
                transform: scale(1) rotateX(0deg);
                border-color: rgba(0, 212, 255, 0.4);
            }
            25% {
                opacity: 0.8;
                transform: scale(1.05) rotateX(90deg);
                border-color: rgba(0, 255, 136, 0.6);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.1) rotateX(180deg);
                border-color: rgba(255, 107, 53, 0.5);
            }
            75% {
                opacity: 0.9;
                transform: scale(1.05) rotateX(270deg);
                border-color: rgba(124, 58, 237, 0.6);
            }
        }

        .data-particle {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            animation: particleFloat 6s ease-in-out infinite;
        }

        .particle-type-1 {
            background: #00ff88;
            box-shadow: 0 0 12px #00ff88, 0 0 24px rgba(0, 255, 136, 0.3);
        }

        .particle-type-2 {
            background: #00d4ff;
            box-shadow: 0 0 12px #00d4ff, 0 0 24px rgba(0, 212, 255, 0.3);
        }

        .particle-type-3 {
            background: #ff6b35;
            box-shadow: 0 0 12px #ff6b35, 0 0 24px rgba(255, 107, 53, 0.3);
        }

        .particle-type-4 {
            background: #7c3aed;
            box-shadow: 0 0 12px #7c3aed, 0 0 24px rgba(124, 58, 237, 0.3);
        }

        @keyframes particleFloat {
            0% {
                transform: translateZ(0px) translateX(0px) translateY(0px);
                opacity: 1;
            }
            25% {
                transform: translateZ(80px) translateX(20px) translateY(-20px);
                opacity: 0.8;
            }
            50% {
                transform: translateZ(120px) translateX(-15px) translateY(25px);
                opacity: 0.4;
            }
            75% {
                transform: translateZ(80px) translateX(-25px) translateY(-10px);
                opacity: 0.7;
            }
            100% {
                transform: translateZ(0px) translateX(0px) translateY(0px);
                opacity: 1;
            }
        }

        .metrics-panel {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 20, 40, 0.8);
            border: 1px solid #00d4ff;
            border-radius: 10px;
            padding: 15px;
            min-width: 200px;
            backdrop-filter: blur(10px);
        }

        .metrics-title {
            font-size: 14px;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 10px;
            text-align: center;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 11px;
        }

        .metric-label {
            color: #8892b0;
        }

        .metric-value {
            color: #00ff88;
            font-weight: 600;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 20, 40, 0.95);
            color: #00d4ff;
            padding: 10px;
            border-radius: 8px;
            font-size: 11px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 2000;
            border: 1px solid #00d4ff;
            backdrop-filter: blur(10px);
        }

        .connection-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            opacity: 0.6;
            animation: connectionPulse 3s ease-in-out infinite;
        }

        @keyframes connectionPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #00ff88;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: statusBlink 2s infinite;
        }

        @keyframes statusBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 智能数据溯源追踪系统</h1>
            <p class="subtitle">3D Data Lineage Visualization Platform</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="startDataFlow()">🚀 启动数据流</button>
            <button class="btn" onclick="highlightTraceability()">🔍 溯源路径</button>
            <button class="btn" onclick="toggle3DView()">🎯 3D视图</button>
            <button class="btn" onclick="resetView()">🔄 重置</button>
        </div>

        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>系统运行中</span>
        </div>

        <div id="visualization">
            <!-- 3D球形数据链路中心 -->
            <div class="sphere-container" id="sphereContainer">
                <div class="data-sphere" id="dataSphere">
                    <div class="sphere-ring" style="width: 80px; height: 80px; top: 110px; left: 110px; animation-delay: 0s;"></div>
                    <div class="sphere-ring" style="width: 120px; height: 120px; top: 90px; left: 90px; animation-delay: 1s;"></div>
                    <div class="sphere-ring" style="width: 160px; height: 160px; top: 70px; left: 70px; animation-delay: 2s;"></div>
                    <div class="sphere-ring" style="width: 200px; height: 200px; top: 50px; left: 50px; animation-delay: 3s;"></div>
                    <div class="sphere-ring" style="width: 240px; height: 240px; top: 30px; left: 30px; animation-delay: 0.5s;"></div>
                    <div class="sphere-ring" style="width: 280px; height: 280px; top: 10px; left: 10px; animation-delay: 1.5s;"></div>
                </div>
            </div>
        </div>

        <div class="metrics-panel">
            <div class="metrics-title">📊 实时监控</div>
            <div class="metric-item">
                <span class="metric-label">数据吞吐:</span>
                <span class="metric-value" id="throughput">2.3 GB/s</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">响应延迟:</span>
                <span class="metric-value" id="latency">8ms</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">成功率:</span>
                <span class="metric-value" id="successRate">99.97%</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">活跃节点:</span>
                <span class="metric-value" id="activeNodes">11</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">溯源深度:</span>
                <span class="metric-value" id="traceDepth">5层</span>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 数据定义
        const nodes = [
            { id: 'edge', name: 'Edge IoT', label: '车载传感器', x: 200, y: 300, color: '#ff6b35', size: 30, status: 'active', angle: 0 },
            { id: 'cloud', name: 'Cloud Storage', label: '分布式存储', x: 350, y: 200, color: '#4f46e5', size: 35, status: 'active', angle: 45 },
            { id: 'backup', name: 'Backup', label: '备份存储', x: 350, y: 400, color: '#6366f1', size: 25, status: 'normal', angle: 90 },
            { id: 'ml', name: 'ML Model', label: '模型训练', x: 500, y: 150, color: '#059669', size: 28, status: 'normal', angle: 135 },
            { id: 'ai', name: 'AI Engine', label: '深度学习处理', x: 600, y: 300, color: '#10b981', size: 40, status: 'selected', gpu: '87%', angle: 180 },
            { id: 'feature', name: 'Feature', label: '特征工程', x: 500, y: 450, color: '#34d399', size: 25, status: 'normal', angle: 225 },
            { id: 'gateway', name: 'Gateway', label: 'API网关', x: 750, y: 200, color: '#dc2626', size: 22, status: 'normal', angle: 270 },
            { id: 'api', name: 'Secure API', label: '加密传输', x: 850, y: 300, color: '#ef4444', size: 32, status: 'active', security: 'TLS 1.3', angle: 315 },
            { id: 'monitor', name: 'Monitor', label: '监控中心', x: 750, y: 400, color: '#f87171', size: 20, status: 'normal', angle: 360 },
            { id: 'blockchain', name: 'Blockchain', label: '存证链', x: 1000, y: 350, color: '#7c3aed', size: 30, status: 'active', block: '#1247', angle: 45 },
            { id: 'enterprise', name: 'Enterprise', label: '企业接收方', x: 1150, y: 300, color: '#8b5cf6', size: 35, status: 'online', angle: 90 }
        ];

        const links = [
            { source: 'edge', target: 'cloud', type: 'main', strength: 0.8 },
            { source: 'edge', target: 'backup', type: 'backup', strength: 0.3 },
            { source: 'cloud', target: 'ai', type: 'main', strength: 0.9 },
            { source: 'cloud', target: 'ml', type: 'normal', strength: 0.5 },
            { source: 'backup', target: 'feature', type: 'normal', strength: 0.4 },
            { source: 'ai', target: 'api', type: 'main', strength: 0.9 },
            { source: 'ai', target: 'gateway', type: 'normal', strength: 0.6 },
            { source: 'feature', target: 'monitor', type: 'normal', strength: 0.4 },
            { source: 'api', target: 'enterprise', type: 'main', strength: 0.8 },
            { source: 'api', target: 'blockchain', type: 'security', strength: 0.7 },
            { source: 'blockchain', target: 'enterprise', type: 'main', strength: 0.8 }
        ];

        // 创建SVG
        const width = window.innerWidth;
        const height = window.innerHeight;
        const svg = d3.select('#visualization')
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .style('position', 'absolute')
            .style('top', 0)
            .style('left', 0);

        // 创建渐变和滤镜
        const defs = svg.append('defs');

        // 发光滤镜
        const glowFilter = defs.append('filter')
            .attr('id', 'glow')
            .attr('x', '-50%')
            .attr('y', '-50%')
            .attr('width', '200%')
            .attr('height', '200%');

        glowFilter.append('feGaussianBlur')
            .attr('stdDeviation', '4')
            .attr('result', 'coloredBlur');

        const feMerge = glowFilter.append('feMerge');
        feMerge.append('feMergeNode').attr('in', 'coloredBlur');
        feMerge.append('feMergeNode').attr('in', 'SourceGraphic');

        // 箭头标记
        const arrowMarker = defs.append('marker')
            .attr('id', 'arrowhead')
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', 8)
            .attr('refY', 0)
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('orient', 'auto');

        arrowMarker.append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('fill', '#00d4ff');

        // 创建连线组
        const linkGroup = svg.append('g').attr('class', 'links');
        const nodeGroup = svg.append('g').attr('class', 'nodes');

        // 绘制连线
        const linkElements = linkGroup.selectAll('.link-path')
            .data(links)
            .enter()
            .append('path')
            .attr('class', 'link-path')
            .attr('stroke', d => {
                switch(d.type) {
                    case 'main': return '#00d4ff';
                    case 'security': return '#ff6b35';
                    case 'backup': return '#6366f1';
                    default: return '#8892b0';
                }
            })
            .attr('stroke-width', d => 2 + d.strength * 2)
            .attr('marker-end', 'url(#arrowhead)')
            .attr('d', d => {
                const source = nodes.find(n => n.id === d.source);
                const target = nodes.find(n => n.id === d.target);
                const dx = target.x - source.x;
                const dy = target.y - source.y;
                const dr = Math.sqrt(dx * dx + dy * dy) * 0.3;
                return `M${source.x},${source.y}A${dr},${dr} 0 0,1 ${target.x},${target.y}`;
            });

        // 绘制节点
        const nodeElements = nodeGroup.selectAll('.node-group')
            .data(nodes)
            .enter()
            .append('g')
            .attr('class', 'node-group')
            .attr('transform', d => `translate(${d.x}, ${d.y})`);

        // 节点外圈（选中状态）
        nodeElements.filter(d => d.status === 'selected')
            .append('circle')
            .attr('r', d => d.size + 8)
            .attr('fill', 'none')
            .attr('stroke', '#fbbf24')
            .attr('stroke-width', 2)
            .attr('class', 'pulse');

        // 节点主体
        nodeElements.append('circle')
            .attr('class', 'node-circle')
            .attr('r', d => d.size)
            .attr('fill', d => d.color)
            .attr('filter', 'url(#glow)')
            .style('color', d => d.color);

        // 节点文字
        nodeElements.append('text')
            .attr('class', 'node-text')
            .attr('y', 0)
            .text(d => d.name);

        // 节点标签
        nodeElements.append('text')
            .attr('class', 'node-label')
            .attr('y', d => d.size + 18)
            .text(d => d.label);

        // 状态指示器
        nodeElements.filter(d => d.gpu)
            .append('text')
            .attr('class', 'node-status')
            .attr('y', d => -d.size - 8)
            .text(d => `GPU: ${d.gpu}`);

        nodeElements.filter(d => d.security)
            .append('text')
            .attr('class', 'node-status')
            .attr('y', d => -d.size - 8)
            .text(d => d.security);

        nodeElements.filter(d => d.block)
            .append('text')
            .attr('class', 'node-status')
            .attr('y', d => -d.size - 8)
            .text(d => `Block ${d.block}`);

        // 活跃状态指示点
        nodeElements.filter(d => d.status === 'active' || d.status === 'online')
            .append('circle')
            .attr('cx', d => d.size - 8)
            .attr('cy', d => -d.size + 8)
            .attr('r', 4)
            .attr('fill', '#00ff88')
            .attr('class', 'pulse');

        // 工具提示
        const tooltip = d3.select('#tooltip');

        nodeElements
            .on('mouseover', function(event, d) {
                tooltip
                    .style('opacity', 1)
                    .style('left', (event.pageX + 10) + 'px')
                    .style('top', (event.pageY - 10) + 'px')
                    .html(`
                        <strong>${d.name}</strong><br/>
                        类型: ${d.label}<br/>
                        状态: ${d.status}<br/>
                        ${d.gpu ? `GPU利用率: ${d.gpu}<br/>` : ''}
                        ${d.security ? `安全协议: ${d.security}<br/>` : ''}
                        ${d.block ? `区块高度: ${d.block}<br/>` : ''}
                    `);
            })
            .on('mouseout', function() {
                tooltip.style('opacity', 0);
            });

        // 添加数据粒子到3D球体
        function addDataParticles() {
            const sphereContainer = document.getElementById('dataSphere');
            const particleTypes = ['particle-type-1', 'particle-type-2', 'particle-type-3', 'particle-type-4'];

            for (let i = 0; i < 40; i++) {
                const particle = document.createElement('div');
                const typeClass = particleTypes[Math.floor(Math.random() * particleTypes.length)];
                particle.className = `data-particle ${typeClass}`;

                // 在球体范围内随机分布
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 120 + 20;
                const x = Math.cos(angle) * radius + 150;
                const y = Math.sin(angle) * radius + 150;

                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (4 + Math.random() * 4) + 's';

                sphereContainer.appendChild(particle);
            }

            // 添加中心核心粒子
            const coreParticle = document.createElement('div');
            coreParticle.className = 'data-particle particle-type-2';
            coreParticle.style.left = '147px';
            coreParticle.style.top = '147px';
            coreParticle.style.width = '12px';
            coreParticle.style.height = '12px';
            coreParticle.style.animationDuration = '3s';
            coreParticle.style.zIndex = '200';
            sphereContainer.appendChild(coreParticle);
        }

        // 功能函数
        let animationActive = false;

        function startDataFlow() {
            if (animationActive) return;
            animationActive = true;

            linkElements
                .filter(d => d.type === 'main')
                .classed('data-flow', true);

            setTimeout(() => {
                linkElements.classed('data-flow', false);
                animationActive = false;
            }, 5000);
        }

        function highlightTraceability() {
            const mainPath = links.filter(d => d.type === 'main');
            linkElements
                .filter(d => d.type === 'main')
                .classed('highlight-path', true);

            setTimeout(() => {
                linkElements.classed('highlight-path', false);
            }, 4000);
        }

        function toggle3DView() {
            const sphere = document.getElementById('sphereContainer');
            sphere.style.display = sphere.style.display === 'none' ? 'block' : 'none';
        }

        function resetView() {
            linkElements
                .classed('highlight-path', false)
                .classed('data-flow', false);
            animationActive = false;
        }

        // 实时更新指标
        function updateMetrics() {
            document.getElementById('throughput').textContent = (2.1 + Math.random() * 0.4).toFixed(1) + ' GB/s';
            document.getElementById('latency').textContent = Math.floor(6 + Math.random() * 4) + 'ms';
            document.getElementById('successRate').textContent = (99.95 + Math.random() * 0.04).toFixed(2) + '%';
            document.getElementById('traceDepth').textContent = Math.floor(4 + Math.random() * 3) + '层';
        }

        // 初始化
        addDataParticles();
        setInterval(updateMetrics, 2000);

        // 自动启动演示
        setTimeout(() => {
            startDataFlow();
        }, 1500);
    </script>
</body>
</html>
