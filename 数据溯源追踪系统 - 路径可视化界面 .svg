<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="700" viewBox="0 0 1200 700">
  <defs>
    <!-- 高级渐变效果 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="containerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <radialGradient id="nodeGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:#64b5f6;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#1976d2;stop-opacity:0.1" />
    </radialGradient>

    <!-- 高级阴影效果 -->
    <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 动画路径效果 -->
    <filter id="pathGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 10 3.5, 0 7" fill="#00e676" />
    </marker>

    <marker id="highlightArrow" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 12 4, 0 8" fill="#ff6b35" />
    </marker>
  </defs>

  <style>
    .container { fill: url(#containerGradient); stroke: #cbd5e0; stroke-width: 1; filter: url(#dropShadow); }
    .header { fill: url(#headerGradient); }
    .header-text { font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif; font-size: 20px; fill: #ffffff; font-weight: 600; letter-spacing: 0.5px; }
    .header-subtitle { font-family: 'SF Pro Display', sans-serif; font-size: 14px; fill: #a0aec0; font-weight: 400; }

    .sidebar { fill: rgba(255,255,255,0.95); stroke: #e2e8f0; stroke-width: 1; filter: url(#dropShadow); backdrop-filter: blur(10px); }
    .sidebar-title { font-family: 'SF Pro Display', sans-serif; font-size: 16px; font-weight: 600; fill: #2d3748; }
    .sidebar-text { font-family: 'SF Mono', Monaco, monospace; font-size: 12px; fill: #4a5568; }
    .sidebar-value { font-family: 'SF Mono', Monaco, monospace; font-size: 12px; fill: #1a202c; font-weight: 500; }

    .visualization-area { fill: rgba(255,255,255,0.8); stroke: #e2e8f0; stroke-width: 1; filter: url(#dropShadow); }
    .grid-line { stroke: #f7fafc; stroke-width: 0.5; stroke-opacity: 0.7; }

    .node { stroke-width: 3; filter: url(#glow); cursor: pointer; transition: all 0.3s ease; }
    .node:hover { transform: scale(1.1); }
    .node-core { fill: url(#nodeGlow); }

    .link { stroke: #a0aec0; stroke-opacity: 0.6; stroke-width: 2; marker-end: url(#arrowhead); }
    .highlight-link { stroke: #ff6b35; stroke-width: 4; stroke-opacity: 0.9; filter: url(#pathGlow); marker-end: url(#highlightArrow); }
    .data-flow { stroke: #00e676; stroke-width: 3; stroke-dasharray: 8,4; marker-end: url(#arrowhead); }

    .node-text { font-family: 'SF Pro Display', sans-serif; font-size: 11px; text-anchor: middle; dominant-baseline: middle; font-weight: 500; fill: #2d3748; }
    .node-label { font-family: 'SF Mono', sans-serif; font-size: 9px; text-anchor: middle; fill: #718096; }

    .toolbar { fill: rgba(248,250,252,0.95); stroke: #e2e8f0; stroke-width: 1; backdrop-filter: blur(10px); }
    .toolbar-icon { font-family: 'SF Pro Display', sans-serif; font-size: 13px; fill: #4a5568; font-weight: 500; cursor: pointer; }
    .toolbar-active { fill: #3182ce; font-weight: 600; }

    .status-indicator { font-family: 'SF Pro Display', sans-serif; font-size: 11px; font-weight: 600; }
    .status-success { fill: #38a169; }
    .status-warning { fill: #d69e2e; }
    .status-error { fill: #e53e3e; }

    .metric-text { font-family: 'SF Mono', sans-serif; font-size: 10px; fill: #718096; }
    .metric-value { font-family: 'SF Mono', sans-serif; font-size: 12px; fill: #2d3748; font-weight: 600; }

    /* 动画效果 */
    .pulse { animation: pulse 2s infinite; }
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .flow-animation { animation: flow 3s linear infinite; }
    @keyframes flow {
      0% { stroke-dashoffset: 0; }
      100% { stroke-dashoffset: -24; }
    }
  </style>

  <!-- Main Container -->
  <rect x="15" y="15" width="1170" height="670" class="container" rx="12" ry="12"/>

  <!-- Header -->
  <path d="M 15 20 A 12 12 0 0 1 27 15 H 1173 A 12 12 0 0 1 1185 27 V 80 H 15 Z" class="header"/>
  <text x="40" y="45" class="header-text">智能数据溯源追踪系统</text>
  <text x="40" y="65" class="header-subtitle">AI-Powered Data Lineage & Traceability Platform</text>

  <!-- 实时状态指示器 -->
  <g transform="translate(950, 35)">
    <circle cx="0" cy="0" r="6" fill="#00e676" class="pulse"/>
    <text x="15" y="5" class="status-indicator status-success">实时监控中</text>
    <text x="100" y="5" class="metric-text">节点: </text>
    <text x="130" y="5" class="metric-value">847</text>
    <text x="160" y="5" class="metric-text">路径: </text>
    <text x="185" y="5" class="metric-value">1,234</text>
  </g>

  <!-- 高级侧边栏 (智能分析面板) -->
  <rect x="900" y="90" width="280" height="590" class="sidebar" rx="8" ry="8"/>

  <!-- AI智能分析区域 -->
  <text x="920" y="120" class="sidebar-title">🤖 AI智能分析</text>
  <rect x="920" y="135" width="240" height="140" style="fill: rgba(59,130,246,0.05); stroke: #3b82f6; stroke-width: 1; rx: 6;"/>
  <text x="930" y="155" class="sidebar-text">异常检测算法:</text>
  <text x="1050" y="155" class="sidebar-value">LSTM+Attention</text>
  <text x="930" y="175" class="sidebar-text">置信度:</text>
  <text x="1050" y="175" class="sidebar-value">97.3%</text>
  <text x="930" y="195" class="sidebar-text">风险评分:</text>
  <text x="1050" y="195" class="status-warning">中等 (6.2/10)</text>
  <text x="930" y="215" class="sidebar-text">预测准确率:</text>
  <text x="1050" y="215" class="sidebar-value">94.7%</text>
  <text x="930" y="235" class="sidebar-text">处理延迟:</text>
  <text x="1050" y="235" class="sidebar-value">12ms</text>
  <text x="930" y="255" class="sidebar-text">吞吐量:</text>
  <text x="1050" y="255" class="sidebar-value">50K ops/s</text>

  <!-- 选中节点详情 -->
  <text x="920" y="305" class="sidebar-title">📊 节点详情 (智能解析)</text>
  <rect x="920" y="320" width="240" height="180" style="fill: rgba(16,185,129,0.05); stroke: #10b981; stroke-width: 1; rx: 6;"/>
  <text x="930" y="340" class="sidebar-text">节点ID:</text>
  <text x="1050" y="340" class="sidebar-value">N-DL-003</text>
  <text x="930" y="360" class="sidebar-text">类型:</text>
  <text x="1050" y="360" class="sidebar-value">深度学习处理</text>
  <text x="930" y="380" class="sidebar-text">算法:</text>
  <text x="1050" y="380" class="sidebar-value">Transformer V3</text>
  <text x="930" y="400" class="sidebar-text">GPU利用率:</text>
  <text x="1050" y="400" class="sidebar-value">87.3%</text>
  <text x="930" y="420" class="sidebar-text">内存占用:</text>
  <text x="1050" y="420" class="sidebar-value">24.6GB</text>
  <text x="930" y="440" class="sidebar-text">处理时间:</text>
  <text x="1050" y="440" class="sidebar-value">2025-08-12 10:30:15</text>
  <text x="930" y="460" class="sidebar-text">状态:</text>
  <text x="1050" y="460" class="status-success">✓ 成功</text>
  <text x="930" y="480" class="sidebar-text">数据量:</text>
  <text x="1050" y="480" class="sidebar-value">2.3TB</text>

  <!-- 高级图例 -->
  <text x="920" y="530" class="sidebar-title">🎨 智能图例</text>
  <g transform="translate(920, 545)">
    <circle cx="10" cy="15" r="8" style="fill: #ff6b35; filter: url(#glow);"/>
    <text x="25" y="20" class="sidebar-text">数据采集 (IoT/Edge)</text>
    <circle cx="10" cy="35" r="8" style="fill: #4f46e5; filter: url(#glow);"/>
    <text x="25" y="40" class="sidebar-text">云端存储 (分布式)</text>
    <circle cx="10" cy="55" r="8" style="fill: #059669; filter: url(#glow);"/>
    <text x="25" y="60" class="sidebar-text">AI处理 (GPU集群)</text>
    <circle cx="10" cy="75" r="8" style="fill: #dc2626; filter: url(#glow);"/>
    <text x="25" y="80" class="sidebar-text">安全传输 (加密)</text>
    <circle cx="10" cy="95" r="8" style="fill: #7c3aed; filter: url(#glow);"/>
    <text x="25" y="100" class="sidebar-text">区块链存证</text>
  </g>

  <!-- 性能指标 -->
  <text x="920" y="660" class="sidebar-title">⚡ 实时性能</text>
  <text x="930" y="675" class="metric-text">延迟: </text>
  <text x="970" y="675" class="metric-value">8ms</text>
  <text x="1020" y="675" class="metric-text">QPS: </text>
  <text x="1050" y="675" class="metric-value">12.5K</text>

  <!-- 主可视化区域 -->
  <rect x="30" y="90" width="860" height="590" class="visualization-area" rx="8" ry="8"/>

  <!-- 网格背景 -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" class="grid-line"/>
    </pattern>
  </defs>
  <rect x="30" y="90" width="860" height="590" fill="url(#grid)" opacity="0.3"/>

  <!-- 高级工具栏 -->
  <rect x="30" y="90" width="860" height="50" class="toolbar" rx="8" ry="8"/>

  <!-- 工具栏图标和功能 -->
  <g transform="translate(50, 115)">
    <g class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#3b82f6" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">🔍</text>
      <text x="0" y="-20" class="toolbar-icon">智能缩放</text>
    </g>

    <g transform="translate(80, 0)" class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#10b981" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">🎯</text>
      <text x="0" y="-20" class="toolbar-icon">路径追踪</text>
    </g>

    <g transform="translate(160, 0)" class="toolbar-icon toolbar-active">
      <circle cx="0" cy="0" r="12" fill="#f59e0b" opacity="0.2"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">⚡</text>
      <text x="0" y="-20" class="toolbar-active">实时分析</text>
    </g>

    <g transform="translate(240, 0)" class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#8b5cf6" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">🤖</text>
      <text x="0" y="-20" class="toolbar-icon">AI预测</text>
    </g>

    <g transform="translate(320, 0)" class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#ef4444" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">🔒</text>
      <text x="0" y="-20" class="toolbar-icon">安全审计</text>
    </g>

    <g transform="translate(400, 0)" class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#06b6d4" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">📊</text>
      <text x="0" y="-20" class="toolbar-icon">性能监控</text>
    </g>

    <g transform="translate(500, 0)" class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#84cc16" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">🎬</text>
      <text x="0" y="-20" class="toolbar-icon">时序回放</text>
    </g>

    <g transform="translate(600, 0)" class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#f97316" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">🌐</text>
      <text x="0" y="-20" class="toolbar-icon">3D视图</text>
    </g>

    <g transform="translate(700, 0)" class="toolbar-icon">
      <circle cx="0" cy="0" r="12" fill="#ec4899" opacity="0.1"/>
      <text x="0" y="4" style="text-anchor: middle; font-size: 16px;">💾</text>
      <text x="0" y="-20" class="toolbar-icon">导出报告</text>
    </g>
  </g>

  <!-- 高级图形可视化 -->
  <g transform="translate(80, 180)">

    <!-- 数据流动路径 (带动画效果) -->
    <path d="M 80 200 Q 200 150 320 200 Q 440 250 560 200 Q 680 150 800 200"
          fill="none" stroke="#00e676" stroke-width="3" stroke-dasharray="12,6"
          class="data-flow flow-animation" opacity="0.7"/>

    <!-- 高亮溯源路径 -->
    <line x1="80" y1="200" x2="200" y2="120" class="highlight-link"/>
    <line x1="200" y1="120" x2="320" y2="200" class="highlight-link"/>
    <line x1="320" y1="200" x2="560" y2="200" class="highlight-link"/>
    <line x1="560" y1="200" x2="680" y2="280" class="highlight-link"/>
    <line x1="680" y1="280" x2="800" y2="200" class="highlight-link"/>

    <!-- 普通连接线 -->
    <line x1="80" y1="200" x2="200" y2="280" class="link"/>
    <line x1="200" y1="280" x2="320" y2="280" class="link"/>
    <line x1="320" y1="280" x2="440" y2="320" class="link"/>
    <line x1="200" y1="120" x2="320" y2="80" class="link"/>
    <line x1="320" y1="80" x2="440" y2="120" class="link"/>

    <!-- 智能节点 (带发光效果) -->
    <!-- 边缘采集节点 -->
    <g class="node">
      <circle cx="80" cy="200" r="25" fill="#ff6b35" class="node-core"/>
      <circle cx="80" cy="200" r="30" fill="none" stroke="#ff6b35" stroke-width="2" opacity="0.3" class="pulse"/>
      <text x="80" y="205" class="node-text">Edge IoT</text>
      <text x="80" y="240" class="node-label">车载传感器</text>
      <circle cx="95" cy="185" r="3" fill="#00e676" class="pulse"/>
    </g>

    <!-- 云端存储集群 -->
    <g class="node">
      <circle cx="200" cy="120" r="28" fill="#4f46e5" class="node-core"/>
      <circle cx="200" cy="120" r="33" fill="none" stroke="#4f46e5" stroke-width="2" opacity="0.3"/>
      <text x="200" y="125" class="node-text">Cloud Storage</text>
      <text x="200" y="160" class="node-label">分布式存储</text>
      <rect x="185" y="105" width="30" height="3" fill="#00e676" opacity="0.8"/>
    </g>

    <g class="node">
      <circle cx="200" cy="280" r="22" fill="#4f46e5" class="node-core"/>
      <text x="200" y="285" class="node-text">Backup</text>
      <text x="200" y="305" class="node-label">备份存储</text>
    </g>

    <!-- AI处理中心 (选中状态) -->
    <g class="node">
      <circle cx="320" cy="200" r="35" fill="#059669" class="node-core"/>
      <circle cx="320" cy="200" r="40" fill="none" stroke="#fbbf24" stroke-width="4" opacity="0.8"/>
      <circle cx="320" cy="200" r="45" fill="none" stroke="#fbbf24" stroke-width="2" opacity="0.4" class="pulse"/>
      <text x="320" y="200" class="node-text">AI Engine</text>
      <text x="320" y="215" class="node-text">Transformer</text>
      <text x="320" y="250" class="node-label">深度学习处理</text>
      <text x="320" y="265" class="node-label">(选中节点)</text>
      <!-- GPU利用率指示器 -->
      <rect x="300" y="175" width="40" height="6" fill="#1f2937" rx="3"/>
      <rect x="300" y="175" width="35" height="6" fill="#10b981" rx="3"/>
      <text x="320" y="170" class="metric-text">GPU: 87%</text>
    </g>

    <g class="node">
      <circle cx="320" cy="80" r="20" fill="#059669" class="node-core"/>
      <text x="320" y="85" class="node-text">ML Model</text>
      <text x="320" y="105" class="node-label">模型训练</text>
    </g>

    <g class="node">
      <circle cx="320" cy="280" r="20" fill="#059669" class="node-core"/>
      <text x="320" y="285" class="node-text">Feature</text>
      <text x="320" y="305" class="node-label">特征工程</text>
    </g>

    <!-- 安全传输节点 -->
    <g class="node">
      <circle cx="560" cy="200" r="26" fill="#dc2626" class="node-core"/>
      <circle cx="560" cy="200" r="31" fill="none" stroke="#dc2626" stroke-width="2" opacity="0.3"/>
      <text x="560" y="205" class="node-text">Secure API</text>
      <text x="560" y="235" class="node-label">加密传输</text>
      <!-- 安全等级指示器 -->
      <polygon points="545,185 575,185 570,175 550,175" fill="#fbbf24"/>
      <text x="560" y="170" class="metric-text">TLS 1.3</text>
    </g>

    <g class="node">
      <circle cx="440" cy="120" r="18" fill="#dc2626" class="node-core"/>
      <text x="440" y="125" class="node-text">Gateway</text>
      <text x="440" y="145" class="node-label">API网关</text>
    </g>

    <g class="node">
      <circle cx="440" cy="320" r="18" fill="#dc2626" class="node-core"/>
      <text x="440" y="325" class="node-text">Monitor</text>
      <text x="440" y="345" class="node-label">监控中心</text>
    </g>

    <!-- 区块链存证节点 -->
    <g class="node">
      <circle cx="680" cy="280" r="24" fill="#7c3aed" class="node-core"/>
      <circle cx="680" cy="280" r="29" fill="none" stroke="#7c3aed" stroke-width="2" opacity="0.3"/>
      <text x="680" y="285" class="node-text">Blockchain</text>
      <text x="680" y="315" class="node-label">存证链</text>
      <!-- 区块链状态 -->
      <rect x="665" y="265" width="30" height="3" fill="#10b981"/>
      <text x="680" y="260" class="metric-text">Block #1247</text>
    </g>

    <!-- 终端接收方 -->
    <g class="node">
      <circle cx="800" cy="200" r="28" fill="#7c3aed" class="node-core"/>
      <circle cx="800" cy="200" r="33" fill="none" stroke="#7c3aed" stroke-width="2" opacity="0.3"/>
      <text x="800" y="205" class="node-text">Enterprise</text>
      <text x="800" y="240" class="node-label">企业接收方</text>
      <circle cx="815" cy="185" r="3" fill="#10b981"/>
      <text x="800" y="175" class="metric-text">在线</text>
    </g>

    <!-- 实时数据流量指示器 -->
    <g transform="translate(400, 350)">
      <rect x="0" y="0" width="200" height="60" fill="rgba(59,130,246,0.05)" stroke="#3b82f6" stroke-width="1" rx="8"/>
      <text x="10" y="20" class="sidebar-title">📈 实时数据流量</text>
      <text x="10" y="35" class="metric-text">当前吞吐量: </text>
      <text x="90" y="35" class="metric-value">2.3 GB/s</text>
      <text x="10" y="50" class="metric-text">延迟: </text>
      <text x="40" y="50" class="metric-value">8ms</text>
      <text x="80" y="50" class="metric-text">成功率: </text>
      <text x="125" y="50" class="metric-value">99.97%</text>
    </g>

  </g>
</svg>