<svg xmlns="http://www.w3.org/2000/svg" width="800" height="300" viewBox="0 0 800 300">
  <style>
    .container { fill: #f8f9fa; stroke: #adb5bd; stroke-width: 1; rx: 5; ry: 5; }
    .block { fill: white; stroke: #007bff; stroke-width: 1; rx: 3; ry: 3; }
    .text { font-family: sans-serif; font-size: 11px; }
    .title { font-family: sans-serif; font-size: 13px; font-weight: bold; text-anchor: middle; }
    .arrow { stroke: #495057; stroke-width: 1; marker-end: url(#arrowhead); }
    .label { font-size: 10px; fill: #6c757d; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="8" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
  </defs>

  <text x="400" y="20" class="title">数据标识与关联技术概念图</text>

  <!-- 1. 分层标识体系 -->
  <rect x="20" y="40" width="240" height="240" class="container"/>
  <text x="140" y="60" class="title">1. 分层标识体系</text>

  <rect x="40" y="80" width="200" height="40" class="block"/>
  <text x="50" y="100" class="text">顶层: 业务标识 (备案ID, 活动ID)</text>

  <line x="140" y="120" x2="140" y2="140" class="arrow"/>

  <rect x="40" y="140" width="200" height="40" class="block"/>
  <text x="50" y="160" class="text">中层: 流程标识 (Trace ID)</text>

  <line x="140" y="180" x2="140" y2="200" class="arrow"/>

  <rect x="40" y="200" width="200" height="60" class="block"/>
  <text x="50" y="220" class="text">底层: 数据标识符 (Data_ID)</text>
  <text x="60" y="240" class="label">雪花算法 (时间戳+机器ID+序列号)</text>
  <text x="60" y="255" class="label">全局唯一且有序</text>

  <!-- 2. 关联关系建立 -->
  <rect x="280" y="40" width="240" height="240" class="container"/>
  <text x="400" y="60" class="title">2. 关联关系建立</text>

  <rect x="300" y="80" width="200" height="40" class="block"/>
  <text x="310" y="100" class="text">在数据处理环节自动记录</text>

  <text x="300" y="140" class="text" style="font-weight: bold;">关联类型:</text>

  <rect x="300" y="160" width="95" height="30" class="block"/>
  <text x="310" y="175" class="text">直接上下游</text>

  <rect x="405" y="160" width="95" height="30" class="block"/>
  <text x="415" y="175" class="text">衍生关系</text>

  <rect x="300" y="210" width="200" height="50" class="block"/>
  <text x="310" y="230" class="text">聚合关系 (复杂关联)</text>
  <text x="320" y="250" class="label">例: 多对一关联,记录所有源ID</text>

  <!-- 3. 元数据角色 -->
  <rect x="540" y="40" width="240" height="240" class="container"/>
  <text x="660" y="60" class="title">3. 元数据管理</text>

  <rect x="560" y="80" width="200" height="100" class="block"/>
  <text x="570" y="100" class="text">记录详细上下文信息:</text>
  <text x="580" y="120" class="label">- 处理时间</text>
  <text x="580" y="135" class="label">- 处理主体 (企业/用户/系统)</text>
  <text x="580" y="150" class="label">- 处理类型 (操作)</text>
  <text x="580" y="165" class="label">- 处理参数</text>

  <rect x="560" y="200" width="200" height="60" class="block"/>
  <text x="570" y="220" class="text">作用: 支撑溯源查询、审计分析</text>
  <text x="570" y="240" class="text">存储: 与业务数据分离,保障性能</text>
</svg>