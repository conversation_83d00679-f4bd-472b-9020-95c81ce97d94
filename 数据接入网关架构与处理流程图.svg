<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .process { fill: #f9f9f9; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">数据接入网关架构与处理流程</text>

  <!-- 数据源层 -->
  <g id="datasources">
    <rect x="20" y="80" width="120" height="80" class="box"/>
    <text x="80" y="105" class="text title">车端数据</text>
    <text x="80" y="125" class="text subtitle">处理日志</text>
    <text x="80" y="140" class="text subtitle">事件数据</text>
    <text x="80" y="155" class="text subtitle">统计数据</text>
  </g>

  <g id="enterprise">
    <rect x="20" y="180" width="120" height="80" class="box"/>
    <text x="80" y="205" class="text title">企业端数据</text>
    <text x="80" y="225" class="text subtitle">操作记录</text>
    <text x="80" y="240" class="text subtitle">备案信息</text>
    <text x="80" y="255" class="text subtitle">监管数据</text>
  </g>

  <!-- Netty网关框架 -->
  <g id="netty-gateway">
    <rect x="180" y="80" width="440" height="360" class="box" stroke-width="3"/>
    <text x="400" y="105" class="text title">Netty数据接入网关（分布式部署）</text>
    
    <!-- 协议解析器 -->
    <rect x="200" y="130" width="140" height="60" class="box process"/>
    <text x="270" y="150" class="text">协议解析器</text>
    <text x="270" y="170" class="text subtitle">通信协议规范</text>

    <!-- 三级验证流程 -->
    <rect x="200" y="210" width="140" height="60" class="box process"/>
    <text x="270" y="230" class="text">①身份认证</text>
    <text x="270" y="250" class="text subtitle">数字证书验证</text>

    <rect x="360" y="210" width="140" height="60" class="box process"/>
    <text x="430" y="230" class="text">②协议校验</text>
    <text x="430" y="250" class="text subtitle">数据包结构验证</text>

    <rect x="200" y="290" width="140" height="60" class="box process"/>
    <text x="270" y="310" class="text">③合规检查</text>
    <text x="270" y="330" class="text subtitle">业务规则验证</text>

    <!-- 数据转换器 -->
    <rect x="360" y="290" width="140" height="60" class="box process"/>
    <text x="430" y="310" class="text">数据转换器</text>
    <text x="430" y="330" class="text subtitle">结构化处理</text>

    <!-- 异步处理器 -->
    <rect x="280" y="370" width="140" height="50" class="box process"/>
    <text x="350" y="390" class="text">异步处理器</text>
    <text x="350" y="405" class="text subtitle">事件驱动模式</text>
  </g>

  <!-- Kafka输出 -->
  <g id="kafka-output">
    <rect x="660" y="130" width="120" height="260" class="box"/>
    <text x="720" y="155" class="text title">Kafka集群</text>
    
    <rect x="670" y="180" width="100" height="40" class="box process"/>
    <text x="720" y="195" class="text subtitle">主题1</text>
    <text x="720" y="210" class="text subtitle">车端数据</text>

    <rect x="670" y="240" width="100" height="40" class="box process"/>
    <text x="720" y="255" class="text subtitle">主题2</text>
    <text x="720" y="270" class="text subtitle">企业数据</text>

    <rect x="670" y="300" width="100" height="40" class="box process"/>
    <text x="720" y="315" class="text subtitle">主题3</text>
    <text x="720" y="330" class="text subtitle">事件数据</text>

    <text x="720" y="370" class="text subtitle">削峰填谷</text>
  </g>

  <!-- Flink处理引擎 -->
  <rect x="300" y="480" width="200" height="60" class="box"/>
  <text x="400" y="505" class="text title">Flink流处理引擎</text>
  <text x="400" y="525" class="text subtitle">实时分析处理</text>

  <!-- 连接线 -->
  <!-- 数据源到网关 -->
  <path d="M 140 120 L 180 120 L 180 160 L 200 160" class="arrow"/>
  <path d="M 140 220 L 180 220 L 180 160 L 200 160" class="arrow"/>

  <!-- 网关内部流程 -->
  <path d="M 340 160 L 370 160 L 370 240 L 360 240" class="arrow"/>
  <path d="M 270 270 L 270 290" class="arrow"/>
  <path d="M 430 270 L 430 290" class="arrow"/>
  <path d="M 340 320 L 360 320" class="arrow"/>
  <path d="M 430 350 L 430 360 L 350 360 L 350 370" class="arrow"/>

  <!-- 网关到Kafka -->
  <path d="M 420 395 L 550 395 L 550 260 L 660 260" class="arrow"/>

  <!-- Kafka到Flink -->
  <path d="M 720 390 L 720 440 L 400 440 L 400 480" class="arrow"/>

  <!-- 标注 -->
  <text x="160" y="150" class="text subtitle" style="font-size: 11px;">TCP/IP</text>
  <text x="160" y="165" class="text subtitle" style="font-size: 11px;">连接</text>
  
  <text x="590" y="250" class="text subtitle" style="font-size: 11px;">异步</text>
  <text x="590" y="265" class="text subtitle" style="font-size: 11px;">发送</text>

  <text x="560" y="455" class="text subtitle" style="font-size: 11px;">消息订阅</text>

  <!-- 关键特性标注 -->
  <g transform="translate(20, 320)">
    <text x="0" y="0" class="text subtitle" text-anchor="start">关键特性：</text>
    <text x="0" y="20" class="text subtitle" text-anchor="start">• 高并发处理</text>
    <text x="0" y="35" class="text subtitle" text-anchor="start">• 多协议支持</text>
    <text x="0" y="50" class="text subtitle" text-anchor="start">• 容错机制</text>
  </g>

  <!-- 性能指标 -->
  <g transform="translate(50, 520)">
    <rect x="0" y="0" width="700" height="50" fill="none" stroke="black" stroke-width="1"/>
    <text x="10" y="20" class="text subtitle" text-anchor="start">性能指标：</text>
    <text x="10" y="35" class="text subtitle" text-anchor="start">• 并发连接数: 10万+  • 数据吞吐量: 10万条/秒  • 验证延迟: <10ms  • 可用性: 99.95%  • 扩展性: 水平扩展</text>
  </g>

</svg>