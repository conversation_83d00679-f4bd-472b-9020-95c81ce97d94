<svg width="800" height="700" viewBox="0 0 800 700" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .component { fill: white; stroke: black; stroke-width: 3; }
      .operator { fill: #f9f9f9; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .label { font-size: 11px; fill: #555; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">Flink流处理架构</text>

  <!-- JobManager集群 -->
  <g id="jobmanager">
    <rect x="100" y="60" width="600" height="100" class="component"/>
    <text x="400" y="85" class="text title">JobManager（高可用集群）</text>
    
    <rect x="120" y="95" width="120" height="50" class="box"/>
    <text x="180" y="115" class="text subtitle">任务调度</text>
    <text x="180" y="130" class="text subtitle">资源管理</text>
    
    <rect x="260" y="95" width="120" height="50" class="box"/>
    <text x="320" y="115" class="text subtitle">检查点协调</text>
    <text x="320" y="130" class="text subtitle">故障恢复</text>
    
    <rect x="400" y="95" width="120" height="50" class="box"/>
    <text x="460" y="115" class="text subtitle">作业管理</text>
    <text x="460" y="130" class="text subtitle">监控统计</text>
    
    <rect x="540" y="95" width="120" height="50" class="box"/>
    <text x="600" y="110" class="text subtitle">ZooKeeper</text>
    <text x="600" y="125" class="text subtitle">分布式协调</text>
  </g>

  <!-- 数据源 -->
  <g id="source">
    <rect x="20" y="220" width="120" height="80" class="box"/>
    <text x="80" y="245" class="text title">Kafka数据源</text>
    <text x="80" y="265" class="text subtitle">车端日志</text>
    <text x="80" y="280" class="text subtitle">企业数据</text>
    <text x="80" y="295" class="text subtitle">事件流</text>
  </g>

  <!-- TaskManager集群 -->
  <g id="taskmanager">
    <rect x="180" y="200" width="480" height="300" class="component"/>
    <text x="420" y="225" class="text title">TaskManager集群（数据处理层）</text>
    
    <!-- 数据清洗算子 -->
    <rect x="200" y="240" width="130" height="80" class="operator"/>
    <text x="265" y="260" class="text">数据清洗算子</text>
    <text x="265" y="275" class="text subtitle">• 格式标准化</text>
    <text x="265" y="290" class="text subtitle">• 质量检测</text>
    <text x="265" y="305" class="text subtitle">• 智能修复</text>
    
    <!-- 时间窗口算子 -->
    <rect x="350" y="240" width="130" height="80" class="operator"/>
    <text x="415" y="260" class="text">时间窗口算子</text>
    <text x="415" y="275" class="text subtitle">• 滑动窗口</text>
    <text x="415" y="290" class="text subtitle">• 滚动窗口</text>
    <text x="415" y="305" class="text subtitle">• 会话窗口</text>
    
    <!-- 业务处理算子 -->
    <rect x="500" y="240" width="130" height="80" class="operator"/>
    <text x="565" y="260" class="text">业务处理算子</text>
    <text x="565" y="275" class="text subtitle">• 风险识别</text>
    <text x="565" y="290" class="text subtitle">• 地理围栏</text>
    <text x="565" y="305" class="text subtitle">• 轨迹分析</text>
    
    <!-- 状态管理 -->
    <rect x="200" y="340" width="280" height="60" class="operator"/>
    <text x="340" y="360" class="text">状态管理（RocksDB）</text>
    <text x="340" y="380" class="text subtitle">• Keyed State • Operator State • CheckPoint</text>
    
    <!-- CEP引擎 -->
    <rect x="500" y="340" width="130" height="60" class="operator"/>
    <text x="565" y="360" class="text">CEP引擎</text>
    <text x="565" y="380" class="text subtitle">复杂事件处理</text>
    
    <!-- 性能优化 -->
    <rect x="200" y="420" width="430" height="60" class="box" stroke-dasharray="3,3"/>
    <text x="415" y="440" class="text">性能优化层</text>
    <text x="415" y="460" class="text subtitle">算子链优化 | 背压处理 | 并行度调优 | 内存管理</text>
  </g>

  <!-- 输出 -->
  <g id="output">
    <rect x="700" y="260" width="80" height="180" class="box"/>
    <text x="740" y="285" class="text title">输出</text>
    <text x="740" y="310" class="text subtitle">Doris</text>
    <text x="740" y="325" class="text subtitle">热数据</text>
    <line x1="710" y1="335" x2="770" y2="335" stroke="black" stroke-width="1"/>
    <text x="740" y="355" class="text subtitle">MinIO</text>
    <text x="740" y="370" class="text subtitle">冷数据</text>
    <line x1="710" y1="380" x2="770" y2="380" stroke="black" stroke-width="1"/>
    <text x="740" y="400" class="text subtitle">告警</text>
    <text x="740" y="415" class="text subtitle">预警</text>
  </g>

  <!-- 连接线 -->
  <!-- JobManager到TaskManager -->
  <path d="M 400 160 L 400 200" class="arrow dashed"/>
  <text x="420" y="180" class="text label">任务分配</text>
  
  <!-- 数据流 -->
  <path d="M 140 260 L 200 260" class="arrow"/>
  <path d="M 330 280 L 350 280" class="arrow"/>
  <path d="M 480 280 L 500 280" class="arrow"/>
  <path d="M 630 280 L 660 280 L 660 350 L 700 350" class="arrow"/>
  
  <!-- 状态管理连接 -->
  <path d="M 265 320 L 265 340" class="arrow dashed"/>
  <path d="M 415 320 L 415 340" class="arrow dashed"/>
  <path d="M 565 320 L 565 340" class="arrow dashed"/>

  <!-- 技术特性说明 -->
  <g transform="translate(50, 540)">
    <rect x="0" y="0" width="700" height="80" fill="none" stroke="black" stroke-width="1"/>
    <text x="10" y="20" class="text subtitle" text-anchor="start">核心技术特性：</text>
    <text x="10" y="35" class="text subtitle" text-anchor="start">• 分布式架构：JobManager主从模式，TaskManager并行处理，支持横向扩展</text>
    <text x="10" y="50" class="text subtitle" text-anchor="start">• 时间语义：Event Time/Processing Time，Watermark机制处理乱序数据</text>
    <text x="10" y="65" class="text subtitle" text-anchor="start">• 容错机制：Exactly-once语义保证，分布式快照，故障自动恢复</text>
    <text x="10" y="80" class="text subtitle" text-anchor="start">• 性能指标：单节点10万条/秒吞吐量，毫秒级延迟，TB级状态管理</text>
  </g>

</svg>