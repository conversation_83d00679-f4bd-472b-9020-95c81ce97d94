<svg width="800" height="650" viewBox="0 0 800 650" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .component { fill: white; stroke: black; stroke-width: 3; }
      .algorithm { fill: #f9f9f9; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .label { font-size: 11px; fill: #555; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">地理空间分析处理流程与算法示意</text>

  <!-- 数据输入 -->
  <g id="input">
    <rect x="20" y="80" width="140" height="100" class="box"/>
    <text x="90" y="105" class="text title">位置数据输入</text>
    <text x="90" y="125" class="text subtitle">• 实时位置</text>
    <text x="90" y="140" class="text subtitle">• 历史轨迹</text>
    <text x="90" y="155" class="text subtitle">• 地理边界</text>
    <text x="90" y="170" class="text subtitle">• 采集日志</text>
  </g>

  <!-- PostGIS核心处理 -->
  <g id="postgis">
    <rect x="200" y="60" width="580" height="140" class="component"/>
    <text x="490" y="85" class="text title">PostGIS地理信息处理引擎</text>
    
    <!-- 预处理模块 -->
    <rect x="220" y="100" width="120" height="80" class="algorithm"/>
    <text x="280" y="120" class="text">数据预处理</text>
    <text x="280" y="140" class="text subtitle">• 坐标系转换</text>
    <text x="280" y="155" class="text subtitle">• 数据清洗</text>
    <text x="280" y="170" class="text subtitle">• 格式标准化</text>
    
    <!-- 空间索引模块 -->
    <rect x="360" y="100" width="120" height="80" class="algorithm"/>
    <text x="420" y="120" class="text">空间索引构建</text>
    <text x="420" y="140" class="text subtitle">• R-Tree索引</text>
    <text x="420" y="155" class="text subtitle">• 网格索引</text>
    <text x="420" y="170" class="text subtitle">• 内存加载</text>
    
    <!-- 基础分析模块 -->
    <rect x="500" y="100" width="120" height="80" class="algorithm"/>
    <text x="560" y="120" class="text">基础空间分析</text>
    <text x="560" y="140" class="text subtitle">• 距离计算</text>
    <text x="560" y="155" class="text subtitle">• 空间关系</text>
    <text x="560" y="170" class="text subtitle">• 缓冲区分析</text>
    
    <!-- 存储管理 -->
    <rect x="640" y="100" width="120" height="80" class="algorithm"/>
    <text x="700" y="120" class="text">存储管理</text>
    <text x="700" y="140" class="text subtitle">• 空间数据存储</text>
    <text x="700" y="155" class="text subtitle">• 索引优化</text>
    <text x="700" y="170" class="text subtitle">• 查询缓存</text>
  </g>

  <!-- 核心算法层 -->
  <g id="algorithms">
    <!-- 地理围栏算法 -->
    <rect x="80" y="240" width="180" height="120" class="component"/>
    <text x="170" y="265" class="text title">地理围栏判断</text>
    <rect x="95" y="280" width="150" height="65" class="box"/>
    <text x="170" y="295" class="text subtitle">• 监管区域检测</text>
    <text x="170" y="310" class="text subtitle">• 禁行区域判断</text>
    <text x="170" y="325" class="text subtitle">• 敏感区域识别</text>
    <text x="170" y="340" class="text subtitle">毫秒级响应</text>
    
    <!-- 轨迹分析算法 -->
    <rect x="300" y="240" width="180" height="120" class="component"/>
    <text x="390" y="265" class="text title">轨迹分析算法</text>
    <rect x="315" y="280" width="150" height="65" class="box"/>
    <text x="390" y="295" class="text subtitle">• 轨迹压缩</text>
    <text x="390" y="310" class="text subtitle">• 轨迹匹配</text>
    <text x="390" y="325" class="text subtitle">• 停留点识别</text>
    <text x="390" y="340" class="text subtitle">• 语义识别</text>
    
    <!-- 热力图生成 -->
    <rect x="520" y="240" width="180" height="120" class="component"/>
    <text x="610" y="265" class="text title">热力图生成</text>
    <rect x="535" y="280" width="150" height="65" class="box"/>
    <text x="610" y="295" class="text subtitle">• 网格化统计</text>
    <text x="610" y="310" class="text subtitle">• 密度估计</text>
    <text x="610" y="325" class="text subtitle">• 时空聚合</text>
    <text x="610" y="340" class="text subtitle">• 可视化渲染</text>
  </g>

  <!-- 输出结果 -->
  <g id="output">
    <rect x="200" y="400" width="400" height="80" class="box"/>
    <text x="400" y="425" class="text title">分析结果输出</text>
    
    <text x="250" y="450" class="text subtitle">违规告警</text>
    <text x="350" y="450" class="text subtitle">轨迹报告</text>
    <text x="450" y="450" class="text subtitle">热点分布</text>
    <text x="550" y="450" class="text subtitle">合规评估</text>
    
    <text x="250" y="465" class="text subtitle">实时推送</text>
    <text x="350" y="465" class="text subtitle">历史查询</text>
    <text x="450" y="465" class="text subtitle">可视化展示</text>
    <text x="550" y="465" class="text subtitle">决策支持</text>
  </g>

  <!-- 连接线 -->
  <!-- 输入到PostGIS -->
  <path d="M 160 130 L 200 130" class="arrow"/>
  
  <!-- PostGIS内部流程 -->
  <path d="M 340 140 L 360 140" class="arrow"/>
  <path d="M 480 140 L 500 140" class="arrow"/>
  <path d="M 620 140 L 640 140" class="arrow"/>
  
  <!-- PostGIS到算法 -->
  <path d="M 280 180 L 280 210 L 170 210 L 170 240" class="arrow"/>
  <path d="M 420 180 L 420 210 L 390 210 L 390 240" class="arrow"/>
  <path d="M 560 180 L 560 210 L 610 210 L 610 240" class="arrow"/>
  
  <!-- 算法到输出 -->
  <path d="M 170 360 L 170 380 L 300 380 L 300 400" class="arrow"/>
  <path d="M 390 360 L 390 400" class="arrow"/>
  <path d="M 610 360 L 610 380 L 500 380 L 500 400" class="arrow"/>

  <!-- 技术说明 -->
  <g transform="translate(50, 510)">
    <rect x="0" y="0" width="700" height="90" fill="none" stroke="black" stroke-width="1"/>
    <text x="350" y="20" class="text subtitle" text-anchor="middle" style="font-weight: bold;">技术特点</text>
    <!-- 左列 -->
    <text x="20" y="40" class="text subtitle" text-anchor="start">• 空间索引：R-Tree结构，支持千万级空间对象快速查询</text>
    <text x="20" y="55" class="text subtitle" text-anchor="start">• 实时性能：地理围栏判断<10ms，轨迹分析<100ms</text>
    <text x="20" y="70" class="text subtitle" text-anchor="start">• 坐标系统：支持WGS84、GCJ02、BD09等多种坐标系</text>
    <!-- 右列 -->
    <text x="370" y="40" class="text subtitle" text-anchor="start">• 算法优化：轨迹压缩率>90%，保持轨迹特征</text>
    <text x="370" y="55" class="text subtitle" text-anchor="start">• 可扩展性：支持自定义空间分析算法集成</text>
    <text x="370" y="70" class="text subtitle" text-anchor="start">• 精度控制：符合国家地理信息安全标准</text>
  </g>

</svg>