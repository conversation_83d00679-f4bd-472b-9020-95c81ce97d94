<svg xmlns="http://www.w3.org/2000/svg" width="600" height="250" viewBox="0 0 600 250">
  <style>
    .center-block { fill: #007bff; stroke: #0056b3; stroke-width: 2; rx: 10; ry: 10; }
    .channel-block { fill: #ffffff; stroke: #6c757d; stroke-width: 1; rx: 5; ry: 5; }
    .escalation-block { fill: #ffc107; stroke: #e0a800; stroke-width: 1; rx: 5; ry: 5; }
    .text { font-family: sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
    .center-text { fill: white; }
    .arrow { stroke: #495057; stroke-width: 1; marker-end: url(#arrowhead); }
    .dashed-arrow { stroke: #dc3545; stroke-width: 1.5; stroke-dasharray: 5,3; marker-end: url(#arrowhead-red); }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="8" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="8" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#dc3545" />
    </marker>
  </defs>

  <text x="300" y="20" class="text" style="font-size: 14px; font-weight: bold;">多通道预警与升级机制简图</text>

  <!-- Center System -->
  <rect x="200" y="90" width="200" height="60" class="center-block"/>
  <text x="300" y="110" class="text center-text">统一消息网关</text>
  <text x="300" y="130" class="text center-text">(预警系统)</text>

  <!-- Channels -->
  <rect x="50" y="50" width="100" height="40" class="channel-block"/>
  <text x="100" y="70" class="text">短信 (SMS)</text>
  <line x="200" y="110" x2="150" y2="70" class="arrow"/>

  <rect x="50" y="150" width="100" height="40" class="channel-block"/>
  <text x="100" y="170" class="text">邮件 (Email)</text>
  <line x="200" y="130" x2="150" y2="170" class="arrow"/>

  <rect x="450" y="50" width="100" height="40" class="channel-block"/>
  <text x="500" y="70" class="text">系统消息</text>
  <line x="400" y="110" x2="450" y2="70" class="arrow"/>

  <rect x="450" y="150" width="100" height="40" class="channel-block"/>
  <text x="500" y="170" class="text">APP推送</text>
  <line x="400" y="130" x2="450" y2="170" class="arrow"/>

  <!-- Escalation Mechanism -->
  <rect x="220" y="190" width="160" height="50" class="escalation-block"/>
  <text x="300" y="205" class="text">预警升级机制</text>
  <text x="300" y="225" class="text">(超时未响应自动上报)</text>

  <line x="300" y="150" x2="300" y2="190" class="arrow"/>

  <!-- Escalation Example (Dashed line) -->
  <text x="500" y="130" class="text" style="font-size: 10px;">(初始接收人)</text>
  <line x="500" y="190" x2="500" y2="220" class="dashed-arrow"/>
  <text x="500" y="235" class="text" style="font-size: 10px;">(上级责任人)</text>
</svg>