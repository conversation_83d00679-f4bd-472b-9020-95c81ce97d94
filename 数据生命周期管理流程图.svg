<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .hot { fill: white; stroke: black; stroke-width: 3; }
      .warm { fill: white; stroke: black; stroke-width: 3; }
      .cold { fill: white; stroke: black; stroke-width: 3; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .label { font-size: 11px; fill: #555; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">数据生命周期管理流程</text>

  <!-- 时间轴 -->
  <g id="timeline">
    <rect x="50" y="60" width="700" height="40" fill="none" stroke="black" stroke-width="1"/>
    <text x="400" y="85" class="text">时间轴</text>
    
    <!-- 时间刻度 -->
    <line x1="200" y1="60" x2="200" y2="100" stroke="black" stroke-width="1"/>
    <text x="200" y="115" class="text subtitle">7天</text>
    
    <line x1="550" y1="60" x2="550" y2="100" stroke="black" stroke-width="1"/>
    <text x="550" y="115" class="text subtitle">12个月</text>
    
    <text x="125" y="85" class="text subtitle">0-7天</text>
    <text x="375" y="85" class="text subtitle">7天-12个月</text>
    <text x="650" y="85" class="text subtitle">>12个月</text>
  </g>

  <!-- 数据新增入口 -->
  <g id="data-entry">
    <rect x="20" y="160" width="120" height="80" class="box" stroke-dasharray="5,5"/>
    <text x="80" y="185" class="text">新增数据</text>
    <text x="80" y="205" class="text subtitle">• 监控数据</text>
    <text x="80" y="220" class="text subtitle">• 告警事件</text>
    <text x="80" y="235" class="text subtitle">• 业务日志</text>
  </g>

  <!-- 热数据层 -->
  <g id="hot-data">
    <rect x="180" y="160" width="160" height="120" class="hot"/>
    <text x="260" y="185" class="text title">热数据</text>
    <text x="260" y="205" class="text subtitle">Doris + SSD存储</text>
    <line x1="190" y1="210" x2="330" y2="210" stroke="black" stroke-width="1"/>
    <text x="260" y="225" class="text subtitle">• 实时监控信息</text>
    <text x="260" y="240" class="text subtitle">• 在线告警事件</text>
    <text x="260" y="255" class="text subtitle">• 即时业务数据</text>
    <text x="260" y="270" class="text subtitle">毫秒级响应</text>
  </g>

  <!-- 温数据层 -->
  <g id="warm-data">
    <rect x="380" y="160" width="160" height="120" class="warm"/>
    <text x="460" y="185" class="text title">温数据</text>
    <text x="460" y="205" class="text subtitle">机械硬盘存储</text>
    <line x1="390" y1="210" x2="530" y2="210" stroke="black" stroke-width="1"/>
    <text x="460" y="225" class="text subtitle">• 历史日志</text>
    <text x="460" y="240" class="text subtitle">• 统计报表</text>
    <text x="460" y="255" class="text subtitle">• 备案信息</text>
    <text x="460" y="270" class="text subtitle">秒级响应</text>
  </g>

  <!-- 冷数据层 -->
  <g id="cold-data">
    <rect x="580" y="160" width="160" height="120" class="cold"/>
    <text x="660" y="185" class="text title">冷数据</text>
    <text x="660" y="205" class="text subtitle">MinIO对象存储</text>
    <line x1="590" y1="210" x2="730" y2="210" stroke="black" stroke-width="1"/>
    <text x="660" y="225" class="text subtitle">• 归档数据</text>
    <text x="660" y="240" class="text subtitle">• 历史案件</text>
    <text x="660" y="255" class="text subtitle">• 长期备份</text>
    <text x="660" y="270" class="text subtitle">分钟级响应</text>
  </g>

  <!-- 数据流转箭头 -->
  <path d="M 140 200 L 180 200" class="arrow"/>
  <path d="M 340 220 L 380 220" class="arrow"/>
  <path d="M 540 220 L 580 220" class="arrow"/>

  <!-- 迁移策略说明 -->
  <g id="migration-strategy">
    <rect x="180" y="320" width="560" height="80" class="box" stroke-dasharray="3,3"/>
    <text x="460" y="340" class="text title">智能迁移策略</text>
    <text x="200" y="360" class="text subtitle" text-anchor="start">• 基于时间：7天后自动迁移至温数据，12个月后迁移至冷数据</text>
    <text x="200" y="375" class="text subtitle" text-anchor="start">• 基于访问频率：访问频率低于阈值触发迁移</text>
    <text x="200" y="390" class="text subtitle" text-anchor="start">• 执行时间：业务低峰期（凌晨2-5点）渐进式迁移</text>
  </g>

  <!-- 数据销毁 -->
  <g id="data-destroy">
    <rect x="300" y="440" width="200" height="80" class="box"/>
    <text x="400" y="465" class="text title">数据销毁</text>
    <text x="400" y="485" class="text subtitle">保存期满（1年）</text>
    <text x="400" y="500" class="text subtitle">• 多次覆写</text>
    <text x="400" y="515" class="text subtitle">• 物理销毁</text>
  </g>

  <!-- 销毁箭头 -->
  <path d="M 660 280 L 660 310 L 400 310 L 400 440" class="arrow dashed"/>
  <text x="530" y="305" class="text label">达到保存期限</text>

  <!-- 迁移说明 -->
  <text x="260" y="155" class="text label">访问频率分析</text>
  <text x="460" y="155" class="text label">智能评估</text>
  <text x="660" y="155" class="text label">归档管理</text>

  <!-- 底部说明 -->
  <g transform="translate(50, 540)">
    <rect x="0" y="0" width="700" height="40" fill="none" stroke="black" stroke-width="1"/>
    <text x="10" y="20" class="text subtitle" text-anchor="start">关键特性：</text>
    <text x="10" y="35" class="text subtitle" text-anchor="start">• 自动化管理：基于规则的自动迁移和销毁  • 成本优化：热温冷分层降低存储成本  • 合规保障：满足数据保存和销毁的法规要求</text>
  </g>

</svg>