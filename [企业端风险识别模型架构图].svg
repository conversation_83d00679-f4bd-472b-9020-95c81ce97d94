<svg xmlns="http://www.w3.org/2000/svg" width="950" height="350" viewBox="0 0 950 350">
  <style>
    .container { fill: white; stroke: black; stroke-width: 1; rx: 5; ry: 5; }
    .block { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 1; rx: 3; ry: 3; }
    .text { font-family: sans-serif; font-size: 13px; font-weight: bold; text-anchor: middle; }
    .subtext { font-family: sans-serif; font-size: 11px; text-anchor: middle; fill: #333; }
    .detail-text { font-family: sans-serif; font-size: 11px; fill: #333; }
    .tech-text { font-family: sans-serif; font-size: 10px; fill: #d9534f; font-style: italic; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>

  <!-- Input Block -->
  <rect x="10" y="150" width="120" height="50" class="container"/>
  <text x="70" y="170" class="text">云端实时信息</text>
  <text x="70" y="190" class="subtext">(协议0x20)</text>

  <line x="130" y="175" x2="160" y2="175" class="arrow"/>

  <!-- Processing Containers -->
  <rect x="160" y="10" width="200" height="330" class="container"/>
  <text x="260" y="30" class="text">数据收集阶段</text>

  <rect x="380" y="10" width="200" height="330" class="container"/>
  <text x="480" y="30" class="text">数据存储加工销毁阶段</text>

  <rect x="600" y="10" width="200" height="330" class="container"/>
  <text x="700" y="30" class="text">数据提供公开阶段</text>

  <!-- Details in Containers -->
  <!-- Collection -->
  <rect x="170" y="50" width="180" height="80" class="block"/>
  <text x="260" y="70" class="detail-text" style="text-anchor: middle;">超资质范围收集</text>
  <text x="180" y="90" class="tech-text">技术: 规则匹配</text>
  <text x="180" y="105" class="tech-text">资质映射</text>

  <rect x="170" y="150" width="180" height="80" class="block"/>
  <text x="260" y="170" class="detail-text" style="text-anchor: middle;">数据来源不合规</text>
  <text x="180" y="190" class="tech-text">技术: 来源合法性校验</text>
  <text x="180" y="205" class="tech-text">授权渠道验证</text>

  <!-- Storage/Processing/Destruction -->
  <rect x="390" y="50" width="180" height="80" class="block"/>
  <text x="480" y="70" class="detail-text" style="text-anchor: middle;">境外存储重要数据</text>
  <text x="400" y="90" class="tech-text">技术: 存储位置识别</text>
  <text x="400" y="105" class="tech-text">网络/物理位置解析</text>

  <rect x="390" y="150" width="180" height="80" class="block"/>
  <text x="480" y="170" class="detail-text" style="text-anchor: middle;">未脱敏加工敏感数据</text>
  <text x="400" y="190" class="tech-text">技术: 机器学习(异常检测)</text>
  <text x="400" y="205" class="tech-text">行为基线分析</text>

  <rect x="390" y="250" width="180" height="80" class="block"/>
  <text x="480" y="270" class="detail-text" style="text-anchor: middle;">销毁流程违规</text>
  <text x="400" y="290" class="tech-text">技术: 流程审计</text>
  <text x="400" y="305" class="tech-text">合规性检查</text>

  <!-- Provision/Publication -->
  <rect x="610" y="50" width="180" height="80" class="block"/>
  <text x="700" y="70" class="detail-text" style="text-anchor: middle;">违规传输</text>
  <text x="620" y="90" class="tech-text">技术: 分布式追踪(Trace ID)</text>
  <text x="620" y="105" class="tech-text">数据流转分析</text>

  <rect x="610" y="150" width="180" height="80" class="block"/>
  <text x="700" y="170" class="detail-text" style="text-anchor: middle;">向能力不足方提供</text>
  <text x="620" y="190" class="tech-text">技术: 接收方资质审查</text>
  <text x="620" y="205" class="tech-text">智能匹配算法</text>

  <rect x="610" y="250" width="180" height="80" class="block"/>
  <text x="700" y="270" class="detail-text" style="text-anchor: middle;">未经评估公开数据</text>
  <text x="620" y="290" class="tech-text">技术: 模式识别(脱敏检测)</text>
  <text x="620" y="305" class="tech-text">评估记录查询</text>

  <!-- Arrows between containers -->
  <line x="360" y="175" x2="380" y2="175" class="arrow"/>
  <line x="580" y="175" x2="600" y2="175" class="arrow"/>
  <line x="800" y="175" x2="820" y2="175" class="arrow"/>

  <!-- Output Block -->
  <rect x="820" y="125" width="120" height="100" class="container"/>
  <text x="880" y="145" class="text">综合风险评估</text>
  <text x="880" y="165" class="subtext">风险聚合&amp;信用评分</text>
  <text x="880" y="180" class="subtext">多因子加权(AHP)</text>
  <text x="880" y="195" class="subtext">动态模型优化</text>
  <text x="880" y="215" class="detail-text">-> 输出风险分值</text>
</svg>