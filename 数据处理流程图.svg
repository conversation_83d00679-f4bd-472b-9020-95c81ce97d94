<?xml version="1.0"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
 <!-- 标题 -->
 <!-- 定义箭头标记 -->
 <defs>
  <marker id="arrowhead" markerHeight="7" markerWidth="10" orient="auto" refX="9" refY="3.5">
   <polygon fill="black" id="svg_1" points="0 0, 10 3.5, 0 7"/>
  </marker>
 </defs>
 <!-- 步骤1: 连接管理 -->
 <!-- 步骤2: 连接维持 -->
 <!-- 步骤3: 数据采集 -->
 <!-- 步骤4: 数据校验 -->
 <!-- 步骤5: 风险检测 -->
 <!-- 步骤6: 数据上报 -->
 <!-- 步骤7: 数据存储 -->
 <!-- 步骤8: 数据审计 -->
 <!-- 连接线 步骤1 → 步骤2 -->
 <!-- 连接线 步骤2 → 步骤3 -->
 <!-- 连接线 步骤3 → 步骤4 (折线，向下) -->
 <!-- 连接线 步骤4 → 步骤5 (折线，向左) -->
 <!-- 连接线 步骤5 → 步骤6 (折线，向左) -->
 <!-- 连接线 步骤6 → 步骤7 (折线，向下) -->
 <!-- 连接线 步骤7 → 步骤8 (折线，向右) -->
 <!-- 循环反馈线：步骤8 → 步骤3 (长折线) -->
 <!-- 循环反馈标签 -->
 <!-- 流程说明框 -->
 <!-- 技术特点框 -->
 <g class="layer">
  <title>Layer 1</title>
  <text fill="black" font-family="Arial, sans-serif" font-size="20" font-weight="bold" id="svg_2" text-anchor="middle" x="501" y="41">数据处理流程图</text>
  <g id="step1"/>
  <g id="step2"/>
  <g id="step3"/>
  <g id="step4"/>
  <g id="step5"/>
  <g id="step6"/>
  <g id="step7"/>
  <g id="step8"/>
  <g id="legend">
   <rect fill="white" height="220" id="svg_44" rx="5" stroke="black" width="400" x="80" y="500"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_45" text-anchor="middle" x="280" y="525">流程说明</text>
   <text fill="black" font-size="12" id="svg_46" x="100" y="550">• 企业数据中心与属地监测平台建立安全连接</text>
   <text fill="black" font-size="12" id="svg_47" x="100" y="570">• 通过心跳机制维持连接稳定性</text>
   <text fill="black" font-size="12" id="svg_48" x="100" y="590">• 实时采集车端和云端数据处理流程信息</text>
   <text fill="black" font-size="12" id="svg_49" x="100" y="610">• 多层次数据校验确保数据质量</text>
   <text fill="black" font-size="12" id="svg_50" x="100" y="630">• 基于风险清单进行智能风险识别</text>
   <text fill="black" font-size="12" id="svg_51" x="100" y="650">• 按协议规范批量上报各类监测数据</text>
   <text fill="black" font-size="12" id="svg_52" x="100" y="670">• 分类存储支持后续查询分析</text>
   <text fill="black" font-size="12" id="svg_53" x="100" y="690">• 定期审计保障合规性</text>
   <text fill="black" font-size="12" font-style="italic" id="svg_54" x="100" y="710">虚线表示持续监测的循环反馈机制</text>
  </g>
  <g id="features">
   <rect fill="white" height="220" id="svg_55" rx="5" stroke="black" width="400" x="520" y="500"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_56" text-anchor="middle" x="720" y="525">技术特点</text>
   <text fill="black" font-size="12" id="svg_57" x="540" y="550">✓ 实时性：秒级数据采集和风险检测</text>
   <text fill="black" font-size="12" id="svg_58" x="540" y="570">✓ 安全性：国密算法加密和数字签名</text>
   <text fill="black" font-size="12" id="svg_59" x="540" y="590">✓ 可靠性：断线重连和数据补发机制</text>
   <text fill="black" font-size="12" id="svg_60" x="540" y="610">✓ 智能性：基于规则引擎和AI的风险识别和预警</text>
   <text fill="black" font-size="12" id="svg_61" x="540" y="630">✓ 标准化：统一协议和数据格式规范</text>
   <text fill="black" font-size="12" id="svg_62" x="540" y="650">✓ 扩展性：支持多业务场景和新需求</text>
   <text fill="black" font-size="12" id="svg_63" x="540" y="670">✓ 审计性：完整的操作日志和溯源能力</text>
   <text fill="black" font-size="12" id="svg_64" x="540" y="690">✓ 合规性：符合国家数据安全法规要求</text>
  </g>
  <g id="svg_65">
   <rect fill="white" height="80" id="svg_3" rx="5" stroke="black" stroke-width="2" width="180" x="171" y="77"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_4" text-anchor="middle" x="261" y="102">1. 连接管理</text>
   <text fill="black" font-size="11" id="svg_5" text-anchor="middle" x="261" y="122">企业数据中心连接到</text>
   <text fill="black" font-size="11" id="svg_6" text-anchor="middle" x="261" y="137">属地监测平台并进行鉴权</text>
   <rect fill="white" height="80" id="svg_7" rx="5" stroke="black" stroke-width="2" width="180" x="411" y="77"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_8" text-anchor="middle" x="501" y="102">2. 连接维持</text>
   <text fill="black" font-size="11" id="svg_9" text-anchor="middle" x="501" y="122">通过心跳机制</text>
   <text fill="black" font-size="11" id="svg_10" text-anchor="middle" x="501" y="137">保持连接活跃</text>
   <rect fill="white" height="80" id="svg_11" rx="5" stroke="black" stroke-width="2" width="180" x="651" y="77"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_12" text-anchor="middle" x="741" y="102">3. 数据采集</text>
   <text fill="black" font-size="11" id="svg_13" text-anchor="middle" x="741" y="122">按照规定采集时空数据</text>
   <text fill="black" font-size="11" id="svg_14" text-anchor="middle" x="741" y="137">处理流程信息</text>
   <rect fill="white" height="80" id="svg_15" rx="5" stroke="black" stroke-width="2" width="180" x="651" y="217"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_16" text-anchor="middle" x="741" y="242">4. 数据校验</text>
   <text fill="black" font-size="11" id="svg_17" text-anchor="middle" x="741" y="262">验证数据的有效性</text>
   <text fill="black" font-size="11" id="svg_18" text-anchor="middle" x="741" y="277">和完整性，核验数据质量</text>
   <rect fill="white" height="80" id="svg_19" rx="5" stroke="black" stroke-width="2" width="180" x="411" y="217"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_20" text-anchor="middle" x="501" y="242">5. 风险检测</text>
   <text fill="black" font-size="11" id="svg_21" text-anchor="middle" x="501" y="262">检测是否存在</text>
   <text fill="black" font-size="11" id="svg_22" text-anchor="middle" x="501" y="277">风险事件</text>
   <rect fill="white" height="80" id="svg_23" rx="5" stroke="black" stroke-width="2" width="180" x="171" y="217"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_24" text-anchor="middle" x="261" y="242">6. 数据上报</text>
   <text fill="black" font-size="11" id="svg_25" text-anchor="middle" x="261" y="262">按照协议规定上报处理</text>
   <text fill="black" font-size="11" id="svg_26" text-anchor="middle" x="261" y="277">流程、事件和统计数据</text>
   <rect fill="white" height="80" id="svg_27" rx="5" stroke="black" stroke-width="2" width="180" x="171" y="357"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_28" text-anchor="middle" x="261" y="382">7. 数据存储</text>
   <text fill="black" font-size="11" id="svg_29" text-anchor="middle" x="261" y="402">保存数据以备</text>
   <text fill="black" font-size="11" id="svg_30" text-anchor="middle" x="261" y="417">后续查询和统计</text>
   <rect fill="white" height="80" id="svg_31" rx="5" stroke="black" stroke-width="2" width="180" x="411" y="357"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_32" text-anchor="middle" x="501" y="382">8. 数据审计</text>
   <text fill="black" font-size="11" id="svg_33" text-anchor="middle" x="501" y="402">定期审计</text>
   <text fill="black" font-size="11" id="svg_34" text-anchor="middle" x="501" y="417">数据处理活动</text>
   <path d="m351,117l30,0l0,0l30,0" fill="none" id="svg_35" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <path d="m591,117l30,0l0,0l30,0" fill="none" id="svg_36" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <path d="m741,157l0,30l0,0l0,30" fill="none" id="svg_37" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <path d="m651,257l-30,0l0,0l-30,0" fill="none" id="svg_38" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <path d="m411,257l-30,0l0,0l-30,0" fill="none" id="svg_39" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <path d="m261,297l0,30l0,0l0,30" fill="none" id="svg_40" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <path d="m351,397l30,0l0,0l30,0" fill="none" id="svg_41" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <path d="m501,357l0,-30l370,0l0,-210l-30,0l-10,0" fill="none" id="svg_42" marker-end="url(#arrowhead)" stroke="black" stroke-dasharray="5,5" stroke-width="2"/>
   <text fill="black" font-size="10" id="svg_43" text-anchor="middle" x="671" y="322">持续监测循环</text>
  </g>
 </g>
</svg>