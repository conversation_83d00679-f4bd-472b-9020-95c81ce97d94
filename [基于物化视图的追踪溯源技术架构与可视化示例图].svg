<svg xmlns="http://www.w3.org/2000/svg" width="900" height="650" viewBox="0 0 900 650">
  <style>
    .title { font-family: sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
    .subtitle { font-family: sans-serif; font-size: 14px; font-weight: bold; fill: #333; }
    .container { fill: none; stroke: #aaa; stroke-width: 1; rx: 5; ry: 5; }
    .base-table { fill: #e3f2fd; stroke: #2196f3; stroke-width: 1; }
    .mv-table { fill: #e8f5e9; stroke: #4caf50; stroke-width: 2; }
    .engine { fill: #fff3e0; stroke: #ff9800; stroke-width: 1; rx: 5; ry: 5; }
    .text { font-family: sans-serif; font-size: 11px; }
    .label { font-family: sans-serif; font-size: 10px; fill: #666; }
    .arrow { stroke: #555; stroke-width: 1; marker-end: url(#arrowhead); }
    .dashed-arrow { stroke: #777; stroke-width: 1; stroke-dasharray: 5,3; marker-end: url(#arrowhead); }
    .optimize-arrow { stroke: #4caf50; stroke-width: 2; marker-end: url(#arrowhead-green); }
    .query-arrow { stroke: #ff9800; stroke-width: 1.5; marker-end: url(#arrowhead-orange); }

    /* Lineage Visualization Styles */
    .lineage-node { stroke-width: 1.5; }
    .lineage-text { font-family: sans-serif; font-size: 10px; text-anchor: middle; dominant-baseline: middle; }
    .lineage-link { stroke: #999; stroke-opacity: 0.6; stroke-width: 1.5; }
    .hotspot-path { stroke: #f44336; stroke-width: 3; stroke-opacity: 0.9; fill: none; rx: 15; ry: 15; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="8" markerHeight="5" refX="8" refY="2.5" orient="auto"><polygon points="0 0, 8 2.5, 0 5" fill="#555" /></marker>
    <marker id="arrowhead-green" markerWidth="8" markerHeight="5" refX="8" refY="2.5" orient="auto"><polygon points="0 0, 8 2.5, 0 5" fill="#4caf50" /></marker>
    <marker id="arrowhead-orange" markerWidth="8" markerHeight="5" refX="8" refY="2.5" orient="auto"><polygon points="0 0, 8 2.5, 0 5" fill="#ff9800" /></marker>
  </defs>

  <text x="450" y="25" class="title">基于物化视图的追踪溯源技术架构与可视化示例</text>

  <!-- Part 1: Data Lineage Visualization Example -->
  <text x="20" y="55" class="subtitle">Part 1: 数据血缘溯源链路可视化示例 (高精地图数据流转)</text>
  <rect x="10" y="65" width="880" height="230" class="container"/>

  <g transform="translate(90, 100)">
    <!-- Hotspot Path Highlight -->
    <rect x="10" y="50" width="720" height="80" class="hotspot-path"/>
    <text x="370" y="40" class="label" style="fill: #f44336; font-weight: bold;">热点溯源路径 (Hotspot Path) - 由物化视图加速</text>

    <!-- Links (Edges) - 主要数据流向 -->
    <line x1="60" y1="90" x2="160" y2="90" class="lineage-link"/>
    <line x1="160" y1="90" x2="260" y2="90" class="lineage-link"/>
    <line x1="260" y1="90" x2="360" y2="90" class="lineage-link"/>
    <line x1="360" y1="90" x2="460" y2="90" class="lineage-link"/>
    <line x1="460" y1="90" x2="560" y2="90" class="lineage-link"/>
    <line x1="560" y1="90" x2="660" y2="90" class="lineage-link"/>
    <!-- Branching paths (Complexity) - 分支数据流向 -->
    <line x1="260" y1="90" x2="360" y2="180" class="lineage-link"/>
    <line x1="360" y1="180" x2="460" y2="180" class="lineage-link"/>
    <line x1="460" y1="90" x2="560" y2="180" class="lineage-link"/>

    <!-- Nodes -->
    <circle cx="60" cy="90" r="30" class="lineage-node" style="fill: #ff7f0e;"/>
    <text x="60" y="90" class="lineage-text">车端采集</text>
    <text x="60" y="135" class="label">ID: D001 (Raw)</text>

    <circle cx="160" cy="90" r="30" class="lineage-node" style="fill: #1f77b4;"/>
    <text x="160" y="90" class="lineage-text">云端存储</text>
    <text x="160" y="135" class="label">(数据湖)</text>

    <circle cx="260" cy="90" r="30" class="lineage-node" style="fill: #2ca02c;"/>
    <text x="260" y="90" class="lineage-text">特征提取</text>
    <text x="260" y="135" class="label">ID: D002 (Feature)</text>

    <circle cx="360" cy="90" r="30" class="lineage-node" style="fill: #1f77b4;"/>
    <text x="360" y="90" class="lineage-text">HD地图库</text>
    <text x="360" y="135" class="label">(存储)</text>

    <circle cx="460" cy="90" r="30" class="lineage-node" style="fill: #2ca02c;"/>
    <text x="460" y="90" class="lineage-text">脱敏处理</text>
    <text x="460" y="135" class="label">ID: D003 (Safe)</text>

    <circle cx="560" cy="90" r="30" class="lineage-node" style="fill: #d62728;"/>
    <text x="560" y="90" class="lineage-text">对外提供</text>
    <text x="560" y="135" class="label">(API调用)</text>

    <circle cx="660" cy="90" r="30" class="lineage-node" style="fill: #9467bd;"/>
    <text x="660" y="90" class="lineage-text">接收方A</text>

    <!-- Branching Nodes -->
    <circle cx="360" cy="180" r="30" class="lineage-node" style="fill: #2ca02c;"/>
    <text x="360" y="180" class="lineage-text">数据标注</text>

    <circle cx="460" cy="180" r="30" class="lineage-node" style="fill: #1f77b4;"/>
    <text x="460" y="180" class="lineage-text">模型训练库</text>

    <circle cx="560" cy="180" r="30" class="lineage-node" style="fill: #d62728;"/>
    <text x="560" y="180" class="lineage-text">内部共享</text>
  </g>

  <!-- Connection Arrow - 连接两个部分 -->
  <line x1="450" y1="295" x2="450" y2="315" class="query-arrow"/>
  <text x="450" y="305" class="label" style="text-anchor: middle;">溯源查询结果输出</text>

  <!-- Part 2: Technical Architecture based on Materialized Views -->
  <text x="20" y="325" class="subtitle">Part 2: 基于物化视图的技术实现架构 (查询性能优化)</text>
  <rect x="10" y="335" width="880" height="280" class="container"/>

  <g transform="translate(30, 360)">
    <!-- Base Tables (Detailed Logs) -->
    <text x="150" y="0" class="text" style="font-weight: bold;">基础溯源日志表 (海量、明细)</text>

    <rect x="10" y="20" width="280" height="80" class="base-table"/>
    <text x="20" y="40" class="text">Table: vehicle_processing_logs</text>
    <text x="20" y="60" class="label">• 按天分区 (Partitioned by Day)</text>
    <text x="20" y="75" class="label">• 包含 log_id, VIN, timestamp...</text>

    <rect x="10" y="120" width="280" height="80" class="base-table"/>
    <text x="20" y="140" class="text">Table: enterprise_processing_logs</text>
    <text x="20" y="160" class="label">• 按天分区</text>
    <text x="20" y="175" class="label">• 包含 log_id, source_log_id (FK), operation...</text>

    <!-- Optimization Process (Pre-calculation) -->
    <!-- 基础表 -> 物化视图 的预计算过程 -->
    <line x1="290" y1="60" x2="350" y2="90" class="optimize-arrow"/>
    <line x1="290" y1="160" x2="350" y2="130" class="optimize-arrow"/>
    <text x="320" y="80" class="label" style="fill: #4caf50; font-size: 9px;">预计算</text>
    <text x="320" y="145" class="label" style="fill: #4caf50; font-size: 9px;">增量更新</text>

    <!-- Materialized View -->
    <rect x="350" y="70" width="250" height="80" class="mv-table"/>
    <text x="360" y="90" class="text" style="font-weight: bold;">MV: mv_hotspot_lineage (物化视图)</text>
    <text x="360" y="110" class="label">• 存储预计算的热点溯源路径</text>
    <text x="360" y="125" class="label">• 结构化存储,查询效率极高</text>
    <text x="360" y="140" class="label">• 对应 Part 1 中的 Hotspot Path</text>

    <!-- Query Engine -->
    <rect x="650" y="50" width="200" height="180" class="engine"/>
    <text x="750" y="75" class="text" style="text-anchor: middle; font-weight: bold;">溯源查询引擎</text>
    <text x="750" y="95" class="text" style="text-anchor: middle; font-weight: bold;">(Optimizer/CBO)</text>

    <!-- Query Path Decision -->
    <!-- 物化视图 -> 查询引擎 (热点查询路径) -->
    <line x1="600" y1="120" x2="650" y2="120" class="query-arrow"/>
    <text x="625" y="110" class="label" style="text-anchor: middle; font-size: 9px;">路径1: 热点查询</text>

    <!-- 基础表 -> 查询引擎 (复杂查询路径) -->
    <line x1="290" y1="220" x2="650" y2="190" class="dashed-arrow"/>
    <text x="470" y="210" class="label" style="text-anchor: middle; font-size: 9px;">路径2: 复杂/低频查询 (直接查询基础表)</text>

    <!-- Optimization Techniques within Engine -->
    <rect x="660" y="140" width="180" height="25" style="fill: #ffecb3; stroke: #ccc;"/>
    <text x="670" y="155" class="text" style="font-size: 10px;">查询重写 (Query Rewrite):</text>
    <text x="780" y="155" class="label" style="font-size: 9px;">自动路由到MV</text>

    <rect x="660" y="175" width="180" height="25" style="fill: #ffecb3; stroke: #ccc;"/>
    <text x="670" y="190" class="text" style="font-size: 10px;">分区裁剪 (Partition Pruning):</text>
    <text x="780" y="190" class="label" style="font-size: 9px;">仅扫描相关时间分区</text>

    <rect x="660" y="205" width="180" height="20" style="fill: #ffecb3; stroke: #ccc;"/>
    <text x="750" y="218" class="text" style="font-size: 10px; text-anchor: middle;">智能查询路由</text>
  </g>
</svg>