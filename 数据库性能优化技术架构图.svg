<svg width="800" height="650" viewBox="0 0 800 650" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .component { fill: white; stroke: black; stroke-width: 3; }
      .module { fill: #f9f9f9; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .label { font-size: 11px; fill: #555; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">数据库性能优化技术架构</text>

  <!-- 应用层 -->
  <g id="app-layer">
    <rect x="250" y="60" width="300" height="50" class="box" stroke-dasharray="5,5"/>
    <text x="400" y="90" class="text">应用层查询请求</text>
  </g>

  <!-- Redis缓存层 -->
  <g id="redis-cache">
    <rect x="100" y="140" width="600" height="80" class="component"/>
    <text x="400" y="165" class="text title">Redis缓存层</text>
    
    <rect x="120" y="175" width="120" height="35" class="box"/>
    <text x="180" y="195" class="text subtitle">热点数据缓存</text>
    
    <rect x="260" y="175" width="120" height="35" class="box"/>
    <text x="320" y="195" class="text subtitle">查询结果缓存</text>
    
    <rect x="400" y="175" width="120" height="35" class="box"/>
    <text x="460" y="195" class="text subtitle">会话缓存</text>
    
    <rect x="540" y="175" width="120" height="35" class="box"/>
    <text x="600" y="195" class="text subtitle">缓存预热/淘汰</text>
  </g>

  <!-- 查询优化层 -->
  <g id="query-optimization">
    <rect x="50" y="250" width="700" height="100" class="component"/>
    <text x="400" y="275" class="text title">查询优化引擎</text>
    
    <rect x="70" y="285" width="140" height="50" class="module"/>
    <text x="140" y="305" class="text">SQL解析器</text>
    <text x="140" y="320" class="text subtitle">语法分析/重写</text>
    
    <rect x="230" y="285" width="140" height="50" class="module"/>
    <text x="300" y="305" class="text">执行计划优化</text>
    <text x="300" y="320" class="text subtitle">成本评估/路径选择</text>
    
    <rect x="390" y="285" width="140" height="50" class="module"/>
    <text x="460" y="305" class="text">统计信息维护</text>
    <text x="460" y="320" class="text subtitle">直方图/采样分析</text>
    
    <rect x="550" y="285" width="140" height="50" class="module"/>
    <text x="620" y="305" class="text">参数调优</text>
    <text x="620" y="320" class="text subtitle">内存/并发配置</text>
  </g>

  <!-- 存储优化层 -->
  <g id="storage-optimization">
    <!-- 索引设计 -->
    <rect x="50" y="380" width="220" height="120" class="component"/>
    <text x="160" y="405" class="text title">索引设计</text>
    <rect x="65" y="420" width="190" height="65" class="box"/>
    <text x="160" y="435" class="text subtitle">• 主键索引（唯一性）</text>
    <text x="160" y="450" class="text subtitle">• 组合索引（多条件）</text>
    <text x="160" y="465" class="text subtitle">• 时间索引（范围查询）</text>
    <text x="160" y="480" class="text subtitle">• 空间索引（地理查询）</text>
    
    <!-- 分区管理 -->
    <rect x="290" y="380" width="220" height="120" class="component"/>
    <text x="400" y="405" class="text title">分区管理</text>
    <rect x="305" y="420" width="190" height="65" class="box"/>
    <text x="400" y="435" class="text subtitle">• 按月时间分区</text>
    <text x="400" y="450" class="text subtitle">• 自动分区创建</text>
    <text x="400" y="465" class="text subtitle">• 历史分区归档</text>
    <text x="400" y="480" class="text subtitle">• 分区修剪优化</text>
    
    <!-- 存储引擎 -->
    <rect x="530" y="380" width="220" height="120" class="component"/>
    <text x="640" y="405" class="text title">存储引擎优化</text>
    <rect x="545" y="420" width="190" height="65" class="box"/>
    <text x="640" y="435" class="text subtitle">• 表空间管理</text>
    <text x="640" y="450" class="text subtitle">• 数据压缩</text>
    <text x="640" y="465" class="text subtitle">• 碎片整理</text>
    <text x="640" y="480" class="text subtitle">• I/O优化</text>
  </g>

  <!-- 性能监控 -->
  <g id="monitoring">
    <rect x="100" y="520" width="600" height="60" class="box" stroke-dasharray="3,3"/>
    <text x="400" y="540" class="text title">性能监控与分析</text>
    
    <text x="200" y="560" class="text subtitle">慢查询日志</text>
    <text x="320" y="560" class="text subtitle">性能指标采集</text>
    <text x="450" y="560" class="text subtitle">瓶颈识别</text>
    <text x="580" y="560" class="text subtitle">优化建议</text>
  </g>

  <!-- 连接线 -->
  <!-- 应用到缓存 -->
  <path d="M 400 110 L 400 140" class="arrow"/>
  <text x="420" y="125" class="text label">查询请求</text>
  
  <!-- 缓存未命中到查询优化 -->
  <path d="M 400 220 L 400 250" class="arrow dashed"/>
  <text x="420" y="235" class="text label">缓存未命中</text>
  
  <!-- 查询优化到存储 -->
  <path d="M 160 350 L 160 380" class="arrow"/>
  <path d="M 400 350 L 400 380" class="arrow"/>
  <path d="M 640 350 L 640 380" class="arrow"/>
  
  <!-- 存储到监控 -->
  <path d="M 160 500 L 160 520" class="arrow dashed"/>
  <path d="M 400 500 L 400 520" class="arrow dashed"/>
  <path d="M 640 500 L 640 520" class="arrow dashed"/>
  
  <!-- 缓存命中返回 -->
  <path d="M 250 180 L 50 180 L 50 85 L 250 85" class="arrow dashed"/>
  <text x="30" y="130" class="text label" text-anchor="middle" transform="rotate(-90 30 130)">缓存命中</text>

  <!-- 优化策略说明 -->
  <g transform="translate(50, 600)">
    <rect x="0" y="0" width="700" height="35" fill="none" stroke="black" stroke-width="1"/>
    <text x="350" y="20" class="text subtitle" text-anchor="middle">优化效果：查询响应时间降低80% | 缓存命中率>90% | 慢查询减少95% | 系统吞吐量提升5倍</text>
  </g>

</svg>