<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400">
  <style>
    .container { fill: #f0f0f0; stroke: #ccc; stroke-width: 1; }
    .header { fill: #4682B4; stroke: #4682B4; stroke-width: 1; }
    .header-text { font-family: sans-serif; font-size: 16px; fill: white; font-weight: bold; }
    .sidebar { fill: #e9ecef; stroke: #ccc; stroke-width: 1; }
    .sidebar-item { font-family: sans-serif; font-size: 14px; fill: #333; }
    .active-item { font-weight: bold; fill: #0056b3; }
    .content { fill: white; stroke: #ccc; stroke-width: 1; }
    .label { font-family: sans-serif; font-size: 14px; fill: #333; }
    .input-box { fill: white; stroke: #aaa; stroke-width: 1; }
    .button { fill: #28a745; stroke: #28a745; stroke-width: 1; rx: 5; ry: 5; }
    .button-text { font-family: sans-serif; font-size: 14px; fill: white; text-anchor: middle; dominant-baseline: middle; }
    .code-editor { font-family: monospace; font-size: 12px; fill: #000; }
  </style>

  <!-- Main Container -->
  <rect x="10" y="10" width="780" height="380" class="container" rx="5" ry="5"/>

  <!-- Header -->
  <path d="M 10 15 A 5 5 0 0 1 15 10 H 785 A 5 5 0 0 1 790 15 V 50 H 10 Z" class="header"/>
  <text x="30" y="35" class="header-text">规则引擎管理系统 - 规则编辑与部署界面 (示意)</text>

  <!-- Sidebar -->
  <rect x="10" y="60" width="180" height="330" class="sidebar"/>
  <text x="30" y="90" class="sidebar-item">规则库列表</text>
  <text x="30" y="120" class="active-item">> 规则编辑 (RT-001)</text>
  <text x="30" y="150" class="sidebar-item">单元测试</text>
  <text x="30" y="180" class="sidebar-item">版本控制</text>
  <text x="30" y="210" class="sidebar-item">发布管理 (热部署)</text>
  <text x="30" y="240" class="sidebar-item">监控与审计</text>

  <!-- Content Area -->
  <rect x="190" y="60" width="600" height="330" class="content"/>

  <text x="210" y="90" class="label">规则ID:</text>
  <rect x="300" y="80" width="100" height="25" class="input-box"/>
  <text x="310" y="95" class="label" style="font-size: 12px;">RT-001</text>

  <text x="420" y="90" class="label">状态:</text>
  <rect x="470" y="80" width="80" height="25" class="input-box" style="fill: #d4edda; stroke: #c3e6cb;"/>
  <text x="480" y="95" class="label" style="font-size: 12px; fill: #155724;">已激活 (v1.2)</text>

  <text x="210" y="130" class="label">规则描述:</text>
  <rect x="300" y="120" width="470" height="25" class="input-box"/>
  <text x="310" y="135" class="label" style="font-size: 12px;">检测车辆进入军事管理区进行数据采集的行为。</text>

  <text x="210" y="170" class="label">规则逻辑 (DRL):</text>
  <!-- Code Editor -->
  <rect x="210" y="180" width="560" height="150" class="input-box" style="fill: #f5f5f5;"/>
  <text x="220" y="200" class="code-editor">rule "RT-001: 军事管理区违规采集"</text>
  <text x="220" y="220" class="code-editor">when</text>
  <text x="240" y="240" class="code-editor">$v: Vehicle( area == "军事管理区", activity == "采集" )</text>
  <text x="220" y="260" class="code-editor">then</text>
  <text x="240" y="280" class="code-editor">insert(new RiskEvent("RT-001", $v.vin, "特别重大"));</text>
  <text x="240" y="300" class="code-editor">log.warn("触发规则 RT-001, VIN: {}", $v.vin);</text>
  <text x="220" y="320" class="code-editor">end</text>

  <!-- Buttons -->
  <rect x="450" y="350" width="100" height="30" class="button" style="fill: #ffc107;"/>
  <text x="500" y="365" class="button-text" style="fill: black;">保存草稿</text>

  <rect x="560" y="350" width="100" height="30" class="button"/>
  <text x="610" y="365" class="button-text">运行测试</text>

  <rect x="670" y="350" width="100" height="30" class="button" style="fill: #007bff;"/>
  <text x="720" y="365" class="button-text">热部署</text>

</svg>