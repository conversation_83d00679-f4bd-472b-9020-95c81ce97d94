<svg width="800" height="500" viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">五步式数据处理技术总体架构</text>

  <!-- 数据源 -->
  <g id="datasource">
    <rect x="20" y="80" width="160" height="100" class="box"/>
    <text x="100" y="120" class="text title">数据源</text>
    <text x="100" y="140" class="text subtitle">车端数据</text>
    <text x="100" y="155" class="text subtitle">企业端数据</text>
    <text x="100" y="170" class="text subtitle">事件数据</text>
  </g>

  <!-- 第一步：Kafka消息队列 -->
  <g id="kafka">
    <rect x="240" y="80" width="160" height="100" class="box"/>
    <text x="320" y="105" class="text title">①Kafka消息队列</text>
    <text x="320" y="125" class="text subtitle">高并发接入</text>
    <text x="320" y="140" class="text subtitle">削峰填谷</text>
    <text x="320" y="155" class="text subtitle">数据缓冲</text>
    <text x="320" y="170" class="text subtitle">3节点集群</text>
  </g>

  <!-- 第二步：Flink数据清洗 -->
  <g id="flink">
    <rect x="460" y="80" width="160" height="100" class="box"/>
    <text x="540" y="105" class="text title">②Flink数据清洗</text>
    <text x="540" y="125" class="text subtitle">格式标准化</text>
    <text x="540" y="140" class="text subtitle">质量校验</text>
    <text x="540" y="155" class="text subtitle">异常过滤</text>
    <text x="540" y="170" class="text subtitle">实时流处理</text>
  </g>

  <!-- 第三步：Doris热数据存储 -->
  <g id="doris">
    <rect x="240" y="240" width="160" height="100" class="box"/>
    <text x="320" y="265" class="text title">③Doris热数据存储</text>
    <text x="320" y="285" class="text subtitle">实时查询</text>
    <text x="320" y="300" class="text subtitle">OLAP分析</text>
    <text x="320" y="315" class="text subtitle">7天热数据</text>
    <text x="320" y="330" class="text subtitle">MPP架构</text>
  </g>

  <!-- 第四步：MinIO冷数据存储 -->
  <g id="minio">
    <rect x="460" y="240" width="160" height="100" class="box"/>
    <text x="540" y="265" class="text title">④MinIO冷数据存储</text>
    <text x="540" y="285" class="text subtitle">长期归档</text>
    <text x="540" y="300" class="text subtitle">对象存储</text>
    <text x="540" y="315" class="text subtitle">1年保存期</text>
    <text x="540" y="330" class="text subtitle">268TB容量</text>
  </g>

  <!-- 第五步：Web数据预警 -->
  <g id="web">
    <rect x="350" y="380" width="160" height="100" class="box"/>
    <text x="430" y="405" class="text title">⑤Web数据预警</text>
    <text x="430" y="425" class="text subtitle">实时监控</text>
    <text x="430" y="440" class="text subtitle">风险预警</text>
    <text x="430" y="455" class="text subtitle">告警推送</text>
    <text x="430" y="470" class="text subtitle">可视化展示</text>
  </g>

  <!-- 数据生命周期管理 -->
  <g id="lifecycle">
    <rect x="680" y="240" width="100" height="100" class="box dashed" stroke-dasharray="5,5"/>
    <text x="730" y="275" class="text subtitle">数据生命</text>
    <text x="730" y="290" class="text subtitle">周期管理</text>
    <text x="730" y="310" class="text subtitle">自动迁移</text>
    <text x="730" y="325" class="text subtitle">成本优化</text>
  </g>

  <!-- 连接线 -->
  <!-- 数据源到Kafka -->
  <path d="M 180 130 L 240 130" class="arrow"/>
  
  <!-- Kafka到Flink -->
  <path d="M 400 130 L 460 130" class="arrow"/>
  
  <!-- Flink分流到Doris和MinIO -->
  <path d="M 540 180 L 540 210 L 320 210 L 320 240" class="arrow"/>
  <path d="M 540 180 L 540 240" class="arrow"/>
  
  <!-- Doris到Web预警 -->
  <path d="M 320 340 L 320 360 L 430 360 L 430 380" class="arrow"/>
  
  <!-- MinIO到Web预警 -->
  <path d="M 540 340 L 540 360 L 430 360 L 430 380" class="arrow"/>
  
  <!-- 生命周期管理连接 -->
  <path d="M 400 290 L 430 290 L 430 290 L 680 290" class="arrow dashed"/>
  <path d="M 620 290 L 650 290 L 650 290 L 680 290" class="arrow dashed"/>

  <!-- 标注 -->
  <text x="210" y="120" class="text subtitle" style="font-size: 11px;">实时接入</text>
  <text x="430" y="120" class="text subtitle" style="font-size: 11px;">流式处理</text>
  <text x="430" y="200" class="text subtitle" style="font-size: 11px;">数据分流</text>
  <text x="260" y="355" class="text subtitle" style="font-size: 11px;">热数据查询</text>
  <text x="500" y="355" class="text subtitle" style="font-size: 11px;">冷数据查询</text>
  <text x="550" y="280" class="text subtitle" style="font-size: 11px;">冷热迁移</text>

  <!-- 性能指标标注 -->
  <g transform="translate(50, 420)">
    <rect x="0" y="0" width="700" height="50" fill="none" stroke="black" stroke-width="1"/>
    <text x="10" y="20" class="text subtitle" text-anchor="start">关键指标：</text>
    <text x="10" y="35" class="text subtitle" text-anchor="start">• Kafka: 10万条/秒吞吐量  • Flink: 毫秒级延迟  • Doris: 秒级查询  • MinIO: PB级存储  • 系统可用性: 99.95%</text>
  </g>

</svg>