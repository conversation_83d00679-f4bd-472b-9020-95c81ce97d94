<svg xmlns="http://www.w3.org/2000/svg" width="850" height="480" viewBox="0 0 850 480">
  <style>
    .section { fill: #f8f9fa; stroke: #adb5bd; stroke-width: 1; rx: 10; ry: 10; }
    .block { fill: white; stroke: #007bff; stroke-width: 1.5; rx: 5; ry: 5; }
    .tech { fill: #e9ecef; stroke: #6c757d; stroke-width: 1; rx: 3; ry: 3; }
    .text { font-family: sans-serif; font-size: 11px; }
    .title { font-family: sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
    .subtitle { font-family: sans-serif; font-size: 12px; font-weight: bold; }
    .arrow { stroke: #495057; stroke-width: 1.5; marker-end: url(#arrowhead); }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="8" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
  </defs>

  <text x="425" y="25" class="title">溯源数据采集、存储与保障技术综合流程图</text>

  <!-- 1. 轻量级溯源信息采集 (5.4.2.1) -->
  <rect x="20" y="50" width="810" height="110" class="section"/>
  <text x="30" y="70" class="subtitle">1. 采集技术 (5.4.2.1): 异步采集、最小侵入、完整性保障</text>

  <rect x="50" y="80" width="180" height="70" class="block"/>
  <text x="60" y="100" class="text">业务系统 (最小侵入)</text>
  <text x="70" y="120" class="text">• AOP切面 (动态织入)</text>
  <text x="70" y="135" class="text">• 本地缓冲区 (非阻塞)</text>

  <line x="230" y="115" x2="270" y2="115" class="arrow"/>
  <text x="250" y="105" class="text" style="text-anchor: middle;">异步</text>

  <rect x="270" y="80" width="180" height="70" class="block"/>
  <text x="280" y="100" class="text">采集线程 (异步处理)</text>
  <text x="290" y="120" class="text">• 生产者-消费者模式</text>
  <text x="290" y="135" class="text">• 异步读取与发送</text>

  <line x="450" y="115" x2="490" y2="115" class="arrow"/>

  <rect x="490" y="80" width="300" height="70" class="tech"/>
  <text x="500" y="100" class="text">关键技术: 完整性保障</text>
  <text x="510" y="120" class="text">• 本地缓存机制 (应对异常)</text>
  <text x="510" y="135" class="text">• 采集确认与重传机制、实时监控</text>

  <line x="425" y="160" x2="425" y2="180" class="arrow"/>

  <!-- 2. 溯源数据分层存储 (5.4.2.2) -->
  <rect x="20" y="180" width="810" height="140" class="section"/>
  <text x="30" y="200" class="subtitle">2. 分层存储 (5.4.2.2): 性能与成本优化、灵活存储模型</text>

  <rect x="50" y="210" width="150" height="50" class="block"/>
  <text x="125" y="230" class="text" style="text-anchor: middle;">实时层 (Redis)</text>
  <text x="125" y="245" class="text" style="text-anchor: middle;">(毫秒级写入)</text>

  <line x="200" y="235" x2="240" y2="235" class="arrow"/>
  <text x="220" y="225" class="text" style="text-anchor: middle;">批量</text>

  <rect x="240" y="210" width="200" height="100" class="block"/>
  <text x="340" y="230" class="text" style="text-anchor: middle;">批处理层 (PostgreSQL)</text>
  <text x="340" y="245" class="text" style="text-anchor: middle;">(持久化与索引)</text>
  <text x="250" y="270" class="text">存储模型:</text>
  <text x="250" y="285" class="text">• 核心信息主表、扩展JSON</text>
  <text x="250" y="300" class="text">• 关联关系表</text>

  <line x="440" y="260" x2="480" y2="260" class="arrow"/>

  <rect x="480" y="230" width="150" height="60" class="block"/>
  <text x="555" y="250" class="text" style="text-anchor: middle;">服务层</text>
  <text x="555" y="270" class="text" style="text-anchor: middle;">(预计算/高效查询)</text>

  <rect x="650" y="210" width="150" height="100" class="tech"/>
  <text x="660" y="230" class="text">关键技术: 存储优化</text>
  <text x="660" y="250" class="text">• 列式压缩存储 (70%压缩率)</text>
  <text x="660" y="270" class="text">• 数据生命周期管理</text>
  <text x="660" y="290" class="text">• 渐进式数据精简</text>

  <line x="425" y="320" x2="425" y2="340" class="arrow"/>

  <!-- 3. 高可用存储保障 (5.4.2.3) -->
  <rect x="20" y="340" width="810" height="120" class="section"/>
  <text x="30" y="360" class="subtitle">3. 高可用保障 (5.4.2.3): 可用性、可靠性、可信性</text>

  <rect x="50" y="370" width="230" height="80" class="block"/>
  <text x="165" y="390" class="text" style="text-anchor: middle;">高可用架构 (核心元素)</text>
  <text x="60" y="410" class="text">• 主从复制与读写分离</text>
  <text x="60" y="425" class="text">• 异步复制与最终一致性</text>
  <text x="60" y="440" class="text">• 秒级自动故障切换</text>

  <rect x="300" y="370" width="230" height="80" class="block"/>
  <text x="415" y="390" class="text" style="text-anchor: middle;">数据备份策略 (关键技术)</text>
  <text x="310" y="410" class="text">• 全量(周)+增量(日)备份</text>
  <text x="310" y="425" class="text">• 异地存储与并行备份</text>
  <text x="310" y="440" class="text">• 定期恢复演练</text>

  <rect x="550" y="370" width="260" height="80" class="block"/>
  <text x="680" y="390" class="text" style="text-anchor: middle;">区块链存证 (核心元素)</text>
  <text x="560" y="410" class="text">• 溯源记录哈希值上链 (不可篡改)</text>
  <text x="560" y="425" class="text">• 默克尔树计算与根哈希上链</text>
  <text x="560" y="440" class="text">• 保证真实可信,控制成本</text>
</svg>