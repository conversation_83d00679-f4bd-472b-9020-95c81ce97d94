<svg viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-size="20" font-weight="bold">溯源追踪与应急管理技术路线</text>
  
  <!-- 顶层输入模块 -->
  <g id="input-modules">
    <!-- 车端数据 -->
    <rect x="100" y="70" width="200" height="100" fill="none" stroke="black" stroke-width="2"/>
    <text x="200" y="100" text-anchor="middle" font-size="14" font-weight="bold">车端数据</text>
    <text x="200" y="125" text-anchor="middle" font-size="12">采集时间</text>
    <text x="200" y="145" text-anchor="middle" font-size="12">位置信息</text>
    
    <!-- 企业端数据 -->
    <rect x="350" y="70" width="200" height="100" fill="none" stroke="black" stroke-width="2"/>
    <text x="450" y="100" text-anchor="middle" font-size="14" font-weight="bold">企业端数据</text>
    <text x="450" y="125" text-anchor="middle" font-size="12">处理日志</text>
    <text x="450" y="145" text-anchor="middle" font-size="12">操作记录</text>
    
    <!-- 监管规范 -->
    <rect x="600" y="70" width="200" height="100" fill="none" stroke="black" stroke-width="2"/>
    <text x="700" y="100" text-anchor="middle" font-size="14" font-weight="bold">监管规范</text>
    <text x="700" y="125" text-anchor="middle" font-size="12">法律法规</text>
    <text x="700" y="145" text-anchor="middle" font-size="12">技术标准</text>
  </g>
  
  <!-- 溯源信息采集与建模 -->
  <rect x="250" y="220" width="400" height="60" fill="none" stroke="black" stroke-width="2"/>
  <text x="450" y="245" text-anchor="middle" font-size="14" font-weight="bold">溯源信息采集与建模</text>
  <text x="450" y="265" text-anchor="middle" font-size="12">轻量化采集 | 图结构建模 | 增量式记录</text>
  
  <!-- 中间层存储 -->
  <g id="middle-storage">
    <!-- 区块链存证 -->
    <rect x="50" y="330" width="220" height="100" fill="none" stroke="black" stroke-width="2"/>
    <text x="160" y="360" text-anchor="middle" font-size="14" font-weight="bold">区块链存证</text>
    <text x="160" y="385" text-anchor="middle" font-size="12">哈希锚定</text>
    <text x="160" y="405" text-anchor="middle" font-size="12">智能合约</text>
    
    <!-- 图数据库 -->
    <rect x="320" y="330" width="220" height="100" fill="none" stroke="black" stroke-width="2"/>
    <text x="430" y="360" text-anchor="middle" font-size="14" font-weight="bold">图数据库</text>
    <text x="430" y="385" text-anchor="middle" font-size="12">溯源关系</text>
    <text x="430" y="405" text-anchor="middle" font-size="12">血缘图谱</text>
    
    <!-- 时序数据库 -->
    <rect x="590" y="330" width="220" height="100" fill="none" stroke="black" stroke-width="2"/>
    <text x="700" y="360" text-anchor="middle" font-size="14" font-weight="bold">时序数据库</text>
    <text x="700" y="385" text-anchor="middle" font-size="12">操作日志</text>
    <text x="700" y="405" text-anchor="middle" font-size="12">事件序列</text>
  </g>
  
  <!-- 右侧功能模块 -->
  <g id="function-modules">
    <!-- 追踪分析引擎 -->
    <rect x="900" y="330" width="220" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="1010" y="355" text-anchor="middle" font-size="13" font-weight="bold">追踪分析引擎</text>
    <text x="1010" y="375" text-anchor="middle" font-size="11">路径还原 | 影响分析</text>
    
    <!-- 异常检测模型 -->
    <rect x="900" y="410" width="220" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="1010" y="435" text-anchor="middle" font-size="13" font-weight="bold">异常检测模型</text>
    <text x="1010" y="455" text-anchor="middle" font-size="11">图神经网络 | ML</text>
    
    <!-- 事件关联分析 -->
    <rect x="900" y="490" width="220" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="1010" y="515" text-anchor="middle" font-size="13" font-weight="bold">事件关联分析</text>
    <text x="1010" y="535" text-anchor="middle" font-size="11">时空关联 | 因果推理</text>
    
    <!-- 应急响应系统 -->
    <rect x="1150" y="230" width="220" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="1260" y="255" text-anchor="middle" font-size="13" font-weight="bold">应急响应系统</text>
    <text x="1260" y="275" text-anchor="middle" font-size="11">预案管理 | 快速处置</text>
    
    <!-- 协同处置平台 -->
    <rect x="1150" y="310" width="220" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="1260" y="335" text-anchor="middle" font-size="13" font-weight="bold">协同处置平台</text>
    <text x="1260" y="355" text-anchor="middle" font-size="11">跨部门联动 | 指挥调度</text>
    
    <!-- 可视化展示 -->
    <rect x="1150" y="390" width="220" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="1260" y="415" text-anchor="middle" font-size="13" font-weight="bold">可视化展示</text>
    <text x="1260" y="435" text-anchor="middle" font-size="11">溯源查询 | 态势感知</text>
    
    <!-- 监管决策支持 -->
    <rect x="1150" y="470" width="220" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="1260" y="495" text-anchor="middle" font-size="13" font-weight="bold">监管决策支持</text>
    <text x="1260" y="515" text-anchor="middle" font-size="11">风险评估 | 合规审查</text>
  </g>
  
  <!-- 连接线 -->
  <!-- 顶层到采集建模 -->
  <line x1="200" y1="170" x2="200" y2="190" stroke="black" stroke-width="1.5"/>
  <line x1="200" y1="190" x2="350" y2="190" stroke="black" stroke-width="1.5"/>
  <line x1="350" y1="190" x2="350" y2="220" stroke="black" stroke-width="1.5"/>
  <polygon points="345,215 350,220 355,215" fill="black"/>
  
  <line x1="450" y1="170" x2="450" y2="220" stroke="black" stroke-width="1.5"/>
  <polygon points="445,215 450,220 455,215" fill="black"/>
  
  <line x1="700" y1="170" x2="700" y2="190" stroke="black" stroke-width="1.5"/>
  <line x1="700" y1="190" x2="550" y2="190" stroke="black" stroke-width="1.5"/>
  <line x1="550" y1="190" x2="550" y2="220" stroke="black" stroke-width="1.5"/>
  <polygon points="545,215 550,220 555,215" fill="black"/>
  
  <!-- 采集建模到中间层 -->
  <line x1="300" y1="280" x2="300" y2="300" stroke="black" stroke-width="1.5"/>
  <line x1="300" y1="300" x2="160" y2="300" stroke="black" stroke-width="1.5"/>
  <line x1="160" y1="300" x2="160" y2="330" stroke="black" stroke-width="1.5"/>
  <polygon points="155,325 160,330 165,325" fill="black"/>
  
  <line x1="450" y1="280" x2="450" y2="330" stroke="black" stroke-width="1.5"/>
  <polygon points="445,325 450,330 455,325" fill="black"/>
  
  <line x1="600" y1="280" x2="600" y2="300" stroke="black" stroke-width="1.5"/>
  <line x1="600" y1="300" x2="700" y2="300" stroke="black" stroke-width="1.5"/>
  <line x1="700" y1="300" x2="700" y2="330" stroke="black" stroke-width="1.5"/>
  <polygon points="695,325 700,330 705,325" fill="black"/>
  
  <!-- 中间层到右侧功能 -->
  <line x1="810" y1="360" x2="850" y2="360" stroke="black" stroke-width="1.5"/>
  <line x1="850" y1="360" x2="850" y2="360" stroke="black" stroke-width="1.5"/>
  <line x1="850" y1="360" x2="900" y2="360" stroke="black" stroke-width="1.5"/>
  <polygon points="895,355 900,360 895,365" fill="black"/>
  
  <line x1="810" y1="380" x2="850" y2="380" stroke="black" stroke-width="1.5"/>
  <line x1="850" y1="380" x2="850" y2="440" stroke="black" stroke-width="1.5"/>
  <line x1="850" y1="440" x2="900" y2="440" stroke="black" stroke-width="1.5"/>
  <polygon points="895,435 900,440 895,445" fill="black"/>
  
  <line x1="810" y1="400" x2="850" y2="400" stroke="black" stroke-width="1.5"/>
  <line x1="850" y1="400" x2="850" y2="520" stroke="black" stroke-width="1.5"/>
  <line x1="850" y1="520" x2="900" y2="520" stroke="black" stroke-width="1.5"/>
  <polygon points="895,515 900,520 895,525" fill="black"/>
  
  <!-- 功能模块到应急系统 -->
  <line x1="1120" y1="360" x2="1135" y2="360" stroke="black" stroke-width="1.5"/>
  <line x1="1135" y1="360" x2="1135" y2="260" stroke="black" stroke-width="1.5"/>
  <line x1="1135" y1="260" x2="1150" y2="260" stroke="black" stroke-width="1.5"/>
  <polygon points="1145,255 1150,260 1145,265" fill="black"/>
  
  <line x1="1120" y1="440" x2="1135" y2="440" stroke="black" stroke-width="1.5"/>
  <line x1="1135" y1="440" x2="1135" y2="340" stroke="black" stroke-width="1.5"/>
  <line x1="1135" y1="340" x2="1150" y2="340" stroke="black" stroke-width="1.5"/>
  <polygon points="1145,335 1150,340 1145,345" fill="black"/>
  
  <line x1="1120" y1="520" x2="1135" y2="520" stroke="black" stroke-width="1.5"/>
  <line x1="1135" y1="520" x2="1135" y2="420" stroke="black" stroke-width="1.5"/>
  <line x1="1135" y1="420" x2="1150" y2="420" stroke="black" stroke-width="1.5"/>
  <polygon points="1145,415 1150,420 1145,425" fill="black"/>
  
  <line x1="1135" y1="500" x2="1150" y2="500" stroke="black" stroke-width="1.5"/>
  <polygon points="1145,495 1150,500 1145,505" fill="black"/>
  
  <!-- 底部阶段 -->
  <g id="phases">
    <!-- 第一阶段 -->
    <rect x="50" y="600" width="300" height="120" fill="none" stroke="black" stroke-width="2"/>
    <text x="200" y="630" text-anchor="middle" font-size="14" font-weight="bold">第一阶段</text>
    <text x="200" y="655" text-anchor="middle" font-size="12">基础能力构建</text>
    <text x="200" y="675" text-anchor="middle" font-size="11">• 采集接口开发</text>
    <text x="200" y="695" text-anchor="middle" font-size="11">• 存储集群搭建</text>
    <text x="200" y="715" text-anchor="middle" font-size="11">• 区块链部署</text>
    
    <!-- 第二阶段 -->
    <rect x="380" y="600" width="300" height="120" fill="none" stroke="black" stroke-width="2"/>
    <text x="530" y="630" text-anchor="middle" font-size="14" font-weight="bold">第二阶段</text>
    <text x="530" y="655" text-anchor="middle" font-size="12">智能化能力提升</text>
    <text x="530" y="675" text-anchor="middle" font-size="11">• ML模型训练</text>
    <text x="530" y="695" text-anchor="middle" font-size="11">• 图计算优化</text>
    <text x="530" y="715" text-anchor="middle" font-size="11">• 自动化响应</text>
    
    <!-- 第三阶段 -->
    <rect x="710" y="600" width="300" height="120" fill="none" stroke="black" stroke-width="2"/>
    <text x="860" y="630" text-anchor="middle" font-size="14" font-weight="bold">第三阶段</text>
    <text x="860" y="655" text-anchor="middle" font-size="12">全面应用推广</text>
    <text x="860" y="675" text-anchor="middle" font-size="11">• 规模化部署</text>
    <text x="860" y="695" text-anchor="middle" font-size="11">• 多级联动</text>
    <text x="860" y="715" text-anchor="middle" font-size="11">• 性能优化</text>
    
    <!-- 第四阶段 -->
    <rect x="1040" y="600" width="300" height="120" fill="none" stroke="black" stroke-width="2"/>
    <text x="1190" y="630" text-anchor="middle" font-size="14" font-weight="bold">第四阶段</text>
    <text x="1190" y="655" text-anchor="middle" font-size="12">持续演进优化</text>
    <text x="1190" y="675" text-anchor="middle" font-size="11">• 技术创新</text>
    <text x="1190" y="695" text-anchor="middle" font-size="11">• 生态建设</text>
    <text x="1190" y="715" text-anchor="middle" font-size="11">• 能力提升</text>
  </g>
  
  <!-- 阶段之间的箭头 -->
  <line x1="350" y1="660" x2="380" y2="660" stroke="black" stroke-width="2"/>
  <polygon points="375,655 380,660 375,665" fill="black"/>
  
  <line x1="680" y1="660" x2="710" y2="660" stroke="black" stroke-width="2"/>
  <polygon points="705,655 710,660 705,665" fill="black"/>
  
  <line x1="1010" y1="660" x2="1040" y2="660" stroke="black" stroke-width="2"/>
  <polygon points="1035,655 1040,660 1035,665" fill="black"/>
</svg>