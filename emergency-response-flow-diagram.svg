<svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="black">应急响应与处置流程架构图</text>
  
  <!-- 事件感知层 -->
  <g id="event-sensing">
    <rect x="50" y="60" width="200" height="80" fill="white" stroke="black" stroke-width="2"/>
    <text x="150" y="95" text-anchor="middle" font-size="14" font-weight="bold">事件感知层</text>
    <text x="150" y="115" text-anchor="middle" font-size="12">溯源追踪｜风险识别</text>
    <text x="150" y="130" text-anchor="middle" font-size="12">安全监测｜异常检测</text>
  </g>
  
  <!-- 事件总线 -->
  <g id="event-bus">
    <rect x="350" y="60" width="500" height="80" fill="white" stroke="black" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="600" y="95" text-anchor="middle" font-size="14" font-weight="bold">事件总线（Apache Kafka）</text>
    <text x="600" y="115" text-anchor="middle" font-size="12">实时事件流｜消息队列｜事件分发</text>
  </g>
  
  <!-- 事件处理引擎 -->
  <g id="event-engine">
    <rect x="950" y="60" width="200" height="80" fill="white" stroke="black" stroke-width="2"/>
    <text x="1050" y="95" text-anchor="middle" font-size="14" font-weight="bold">事件处理引擎</text>
    <text x="1050" y="115" text-anchor="middle" font-size="12">复杂事件处理(CEP)</text>
    <text x="1050" y="130" text-anchor="middle" font-size="12">关联分析｜模式识别</text>
  </g>
  
  <!-- 智能决策层 -->
  <g id="decision-layer">
    <!-- 级别判定 -->
    <rect x="200" y="200" width="180" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="290" y="230" text-anchor="middle" font-size="14" font-weight="bold">级别判定</text>
    <text x="290" y="250" text-anchor="middle" font-size="12">风险评估模型</text>
    <text x="290" y="270" text-anchor="middle" font-size="12">I级｜II级｜III级｜IV级</text>
    <text x="290" y="290" text-anchor="middle" font-size="12">模糊逻辑判定</text>
    
    <!-- 预案匹配 -->
    <rect x="420" y="200" width="180" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="510" y="230" text-anchor="middle" font-size="14" font-weight="bold">预案匹配</text>
    <text x="510" y="250" text-anchor="middle" font-size="12">知识图谱</text>
    <text x="510" y="270" text-anchor="middle" font-size="12">案例推理(CBR)</text>
    <text x="510" y="290" text-anchor="middle" font-size="12">规则推理(RBR)</text>
    
    <!-- 决策引擎 -->
    <rect x="640" y="200" width="180" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="730" y="230" text-anchor="middle" font-size="14" font-weight="bold">决策引擎</text>
    <text x="730" y="250" text-anchor="middle" font-size="12">智能匹配算法</text>
    <text x="730" y="270" text-anchor="middle" font-size="12">强化学习优化</text>
    <text x="730" y="290" text-anchor="middle" font-size="12">处置方案生成</text>
  </g>
  
  <!-- 执行层 -->
  <g id="execution-layer">
    <!-- 快速响应 -->
    <rect x="50" y="360" width="200" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="150" y="390" text-anchor="middle" font-size="14" font-weight="bold">快速响应执行</text>
    <text x="150" y="410" text-anchor="middle" font-size="12">数据访问阻断</text>
    <text x="150" y="430" text-anchor="middle" font-size="12">账号权限冻结</text>
    <text x="150" y="450" text-anchor="middle" font-size="12">系统网络隔离</text>
    
    <!-- 协同处置平台 -->
    <rect x="300" y="360" width="300" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="450" y="390" text-anchor="middle" font-size="14" font-weight="bold">协同处置平台</text>
    <text x="450" y="410" text-anchor="middle" font-size="12">统一指挥调度｜跨部门协同</text>
    <text x="450" y="430" text-anchor="middle" font-size="12">即时通讯｜视频会议｜文件共享</text>
    <text x="450" y="450" text-anchor="middle" font-size="12">工作流引擎(BPMN 2.0)</text>
    
    <!-- 应急资源 -->
    <rect x="650" y="360" width="200" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="750" y="390" text-anchor="middle" font-size="14" font-weight="bold">应急资源调配</text>
    <text x="750" y="410" text-anchor="middle" font-size="12">人员调度</text>
    <text x="750" y="430" text-anchor="middle" font-size="12">技术工具</text>
    <text x="750" y="450" text-anchor="middle" font-size="12">外部支持</text>
  </g>
  
  <!-- 责任主体 -->
  <g id="responsibility">
    <rect x="900" y="360" width="250" height="100" fill="white" stroke="black" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="1025" y="390" text-anchor="middle" font-size="14" font-weight="bold">责任主体</text>
    <text x="1025" y="410" text-anchor="middle" font-size="12">国家监管中心</text>
    <text x="1025" y="430" text-anchor="middle" font-size="12">属地监管中心</text>
    <text x="1025" y="450" text-anchor="middle" font-size="12">企业安全团队</text>
  </g>
  
  <!-- 记录与评估层 -->
  <g id="record-layer">
    <!-- 区块链存证 -->
    <rect x="200" y="520" width="180" height="80" fill="white" stroke="black" stroke-width="2"/>
    <text x="290" y="550" text-anchor="middle" font-size="14" font-weight="bold">区块链存证</text>
    <text x="290" y="570" text-anchor="middle" font-size="12">处置记录上链</text>
    <text x="290" y="590" text-anchor="middle" font-size="12">不可篡改审计</text>
    
    <!-- 效果评估 -->
    <rect x="420" y="520" width="180" height="80" fill="white" stroke="black" stroke-width="2"/>
    <text x="510" y="550" text-anchor="middle" font-size="14" font-weight="bold">效果评估</text>
    <text x="510" y="570" text-anchor="middle" font-size="12">定量+定性指标</text>
    <text x="510" y="590" text-anchor="middle" font-size="12">自动评估报告</text>
    
    <!-- 持续改进 -->
    <rect x="640" y="520" width="180" height="80" fill="white" stroke="black" stroke-width="2"/>
    <text x="730" y="550" text-anchor="middle" font-size="14" font-weight="bold">持续改进</text>
    <text x="730" y="570" text-anchor="middle" font-size="12">PDCA循环</text>
    <text x="730" y="590" text-anchor="middle" font-size="12">知识沉淀</text>
  </g>
  
  <!-- 预案知识库 -->
  <g id="knowledge-base">
    <rect x="50" y="650" width="1100" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="600" y="680" text-anchor="middle" font-size="14" font-weight="bold">预案知识库</text>
    <text x="600" y="705" text-anchor="middle" font-size="12">数据泄露预案｜非法采集预案｜越权访问预案｜跨境传输预案｜系统故障预案</text>
    <text x="600" y="730" text-anchor="middle" font-size="12">历史案例库｜专家经验库｜最佳实践库｜法规标准库</text>
  </g>
  
  <!-- 连接线 -->
  <!-- 事件感知到事件总线 -->
  <path d="M 250 100 L 350 100" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 事件总线到事件处理引擎 -->
  <path d="M 850 100 L 950 100" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 事件处理引擎到决策层 -->
  <path d="M 1050 140 L 1050 170 L 730 170 L 730 200" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 决策层内部连接 -->
  <path d="M 380 250 L 420 250" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 600 250 L 640 250" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 决策层到执行层 -->
  <path d="M 290 300 L 290 330 L 150 330 L 150 360" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 510 300 L 510 330 L 450 330 L 450 360" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 730 300 L 730 330 L 750 330 L 750 360" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 执行层到责任主体 -->
  <path d="M 850 410 L 900 410" fill="none" stroke="black" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- 执行层到记录层 -->
  <path d="M 150 460 L 150 490 L 290 490 L 290 520" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 450 460 L 450 490 L 510 490 L 510 520" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 750 460 L 750 490 L 730 490 L 730 520" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 记录层到知识库 -->
  <path d="M 290 600 L 290 620 L 600 620 L 600 650" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 510 600 L 510 620 L 600 620 L 600 650" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 730 600 L 730 620 L 600 620 L 600 650" fill="none" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 知识库反馈到预案匹配 -->
  <path d="M 100 650 L 100 170 L 510 170 L 510 200" fill="none" stroke="black" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>
</svg>