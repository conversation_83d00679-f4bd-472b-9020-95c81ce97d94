<svg width="800" height="620" viewBox="0 0 800 620" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .hot-storage { fill: white; stroke: black; stroke-width: 3; }
      .cold-storage { fill: white; stroke: black; stroke-width: 3; }
      .db-storage { fill: white; stroke: black; stroke-width: 3; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .dashed { stroke-dasharray: 5,5; }
      .label { font-size: 11px; fill: #555; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">热-冷分层存储架构</text>

  <!-- 数据接入层 -->
  <g id="data-input">
    <rect x="50" y="80" width="700" height="60" class="box" stroke-dasharray="5,5"/>
    <text x="400" y="100" class="text">数据接入层</text>
    <text x="150" y="120" class="text subtitle">实时监控数据</text>
    <text x="280" y="120" class="text subtitle">告警事件</text>
    <text x="400" y="120" class="text subtitle">业务日志</text>
    <text x="520" y="120" class="text subtitle">备案信息</text>
    <text x="650" y="120" class="text subtitle">历史数据</text>
  </g>

  <!-- 热数据存储 - Doris -->
  <g id="hot-storage">
    <rect x="100" y="200" width="250" height="160" class="hot-storage"/>
    <text x="225" y="230" class="text title">Doris 热数据存储</text>
    <text x="225" y="250" class="text subtitle">MPP架构 / 列式存储</text>
    
    <rect x="120" y="270" width="210" height="70" class="box"/>
    <text x="225" y="290" class="text subtitle">• 近7天监控数据</text>
    <text x="225" y="305" class="text subtitle">• 实时告警事件</text>
    <text x="225" y="320" class="text subtitle">• 在线业务数据</text>
    <text x="225" y="335" class="text subtitle">• 高频查询数据</text>
  </g>

  <!-- 冷数据存储 - MinIO -->
  <g id="cold-storage">
    <rect x="450" y="200" width="250" height="160" class="cold-storage"/>
    <text x="575" y="230" class="text title">MinIO 冷数据存储</text>
    <text x="575" y="250" class="text subtitle">对象存储 / 分布式架构</text>
    
    <rect x="470" y="270" width="210" height="70" class="box"/>
    <text x="575" y="290" class="text subtitle">• 历史日志数据（>7天）</text>
    <text x="575" y="305" class="text subtitle">• 归档文件</text>
    <text x="575" y="320" class="text subtitle">• 备份数据</text>
    <text x="575" y="335" class="text subtitle">• 低频访问数据</text>
  </g>

  <!-- 关系型数据库 - PostgreSQL -->
  <g id="db-storage">
    <rect x="275" y="410" width="250" height="100" class="db-storage"/>
    <text x="400" y="435" class="text title">PostgreSQL</text>
    <text x="400" y="455" class="text subtitle">主从复制 / 读写分离</text>
    
    <text x="400" y="475" class="text subtitle">• 企业备案信息</text>
    <text x="400" y="490" class="text subtitle">• 车辆注册数据</text>
    <text x="400" y="505" class="text subtitle">• 用户权限信息</text>
  </g>

  <!-- 数据生命周期管理 -->
  <g id="lifecycle">
    <rect x="380" y="280" width="40" height="60" class="box" stroke-dasharray="3,3"/>
    <text x="400" y="300" class="text subtitle" style="font-size: 10px;" transform="rotate(-90 400 300)">生命周期</text>
    <text x="400" y="315" class="text subtitle" style="font-size: 10px;" transform="rotate(-90 400 315)">管理</text>
  </g>

  <!-- 连接线 -->
  <!-- 实时监控数据到Doris -->
  <path d="M 150 140 L 150 200" class="arrow"/>
  <!-- 告警事件到Doris -->
  <path d="M 280 140 L 280 170 L 225 170 L 225 200" class="arrow"/>
  <!-- 业务日志到MinIO -->
  <path d="M 400 140 L 400 170 L 575 170 L 575 200" class="arrow"/>
  <!-- 历史数据到MinIO -->
  <path d="M 650 140 L 650 200" class="arrow"/>
  <!-- 备案信息到PostgreSQL - 从右边绕过生命周期管理框 -->
  <path d="M 520 140 L 520 160 L 730 160 L 730 390 L 400 390 L 400 410" class="arrow"/>

  <!-- 热数据到冷数据迁移 -->
  <path d="M 350 305 L 380 305" class="arrow dashed"/>
  <path d="M 420 305 L 450 305" class="arrow dashed"/>
  <text x="400" y="295" class="text label">7天后自动迁移</text>

  <!-- 性能指标 -->
  <g transform="translate(120, 370)">
    <text x="0" y="0" class="text subtitle" text-anchor="start">性能指标：</text>
    <text x="0" y="15" class="text subtitle" text-anchor="start">• 秒级查询响应</text>
    <text x="0" y="30" class="text subtitle" text-anchor="start">• 高并发支持</text>
  </g>

  <g transform="translate(470, 370)">
    <text x="0" y="0" class="text subtitle" text-anchor="start">存储特性：</text>
    <text x="0" y="15" class="text subtitle" text-anchor="start">• PB级存储容量</text>
    <text x="0" y="30" class="text subtitle" text-anchor="start">• 低成本长期保存</text>
  </g>

  <!-- 底部说明 -->
  <g transform="translate(50, 530)">
    <rect x="0" y="0" width="700" height="50" fill="none" stroke="black" stroke-width="1"/>
    <text x="10" y="20" class="text subtitle" text-anchor="start">架构优势：</text>
    <text x="10" y="35" class="text subtitle" text-anchor="start">• 热冷分层：根据访问频率优化存储成本  • 自动迁移：7天热数据自动转冷存储  • 高可用：主从复制保障数据安全</text>
    <text x="10" y="50" class="text subtitle" text-anchor="start">• 弹性扩展：支持横向扩容  • 多引擎协同：Doris实时分析、MinIO长期归档、PostgreSQL事务处理</text>
  </g>

</svg>