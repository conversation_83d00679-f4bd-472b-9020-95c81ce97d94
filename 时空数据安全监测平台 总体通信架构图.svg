<?xml version="1.0"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
 <!-- 背景 -->
 <!-- 标题 -->
 <!-- 车端层 -->
 <!-- 企业端层 -->
 <!-- 政府端层（属地监测平台） -->
 <!-- 国家端层（预留） -->
 <!-- 车端到企业端通信 -->
 <!-- 企业端到政府端通信 -->
 <!-- 政府端到国家端通信（预留） -->
 <!-- 数据分类图例 -->
 <!-- 通信机制说明 -->
 <!-- 箭头定义 -->
 <defs>
  <marker id="arrowhead" markerHeight="10" markerWidth="10" orient="auto" refX="8" refY="3">
   <polygon fill="black" id="svg_1" points="0 0, 10 3, 0 6"/>
  </marker>
  <marker id="arrowhead2" markerHeight="10" markerWidth="10" orient="auto" refX="8" refY="3">
   <polygon fill="black" id="svg_2" points="0 0, 10 3, 0 6"/>
  </marker>
  <marker id="arrowhead-gray" markerHeight="10" markerWidth="10" orient="auto" refX="8" refY="3">
   <polygon fill="gray" id="svg_3" points="0 0, 10 3, 0 6"/>
  </marker>
 </defs>
 <g class="layer">
  <title>Layer 1</title>
  <text fill="black" font-size="24" font-weight="bold" id="svg_5" style="cursor: move;" text-anchor="middle" x="598" y="67">时空数据安全监测平台 总体通信架构图</text>
  <g id="vehicle-layer">
   <rect fill="white" height="135.999999" id="svg_6" stroke="black" stroke-width="2" width="391.000009" x="713" y="440"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_7" text-anchor="middle" x="912" y="482">车端</text>
   <text font-size="12" id="svg_8" text-anchor="middle" x="912" y="507">（车载终端）</text>
   <text font-size="12" id="svg_9" text-anchor="middle" x="912" y="532">• 数据采集设备</text>
   <text font-size="12" id="svg_10" text-anchor="middle" x="912" y="552">• 监管插件</text>
  </g>
  <g id="enterprise-layer">
   <rect fill="white" height="140" id="svg_11" stroke="black" stroke-width="2" width="397.000031" x="100" y="437"/>
   <text font-size="16" font-weight="bold" id="svg_12" text-anchor="middle" x="300" y="467">企业端</text>
   <text font-size="12" id="svg_13" text-anchor="middle" x="300" y="492">（企业数据中心）</text>
   <text font-size="12" id="svg_14" text-anchor="middle" x="300" y="517">• 数据汇聚处理</text>
   <text fill="black" font-size="12" id="svg_15" text-anchor="middle" x="300" y="537">• 监管日志生成</text>
   <text font-size="12" id="svg_16" text-anchor="middle" x="300" y="557">• 统计分析</text>
  </g>
  <g id="local-gov-layer">
   <rect fill="white" height="136" id="svg_17" stroke="black" stroke-width="2" width="400" x="100" y="120"/>
   <text fill="black" font-size="16" font-weight="bold" id="svg_18" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="302" y="147.82355">政府端</text>
   <text fill="black" font-size="12" id="svg_19" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="173.82355">（属地监测平台）</text>
   <text font-size="12" id="svg_20" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="198.82355">• 实时监控</text>
   <text font-size="12" id="svg_21" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="218.82355">• 风险预警</text>
   <text font-size="12" id="svg_22" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="238.82355">• 合规审查</text>
  </g>
  <g id="national-layer">
   <rect fill="white" height="140" id="svg_23" stroke="black" stroke-dasharray="5,5" stroke-width="2" width="400" x="710" y="117"/>
   <text fill="gray" font-size="16" font-weight="bold" id="svg_24" text-anchor="middle" x="910" y="147">国家端（预留）</text>
   <text fill="gray" font-size="12" id="svg_25" text-anchor="middle" x="910" y="172">（国家监测平台）</text>
   <text fill="gray" font-size="12" id="svg_26" text-anchor="middle" x="910" y="197">• 宏观监管</text>
   <text fill="gray" font-size="12" id="svg_27" text-anchor="middle" x="910" y="217">• 政策制定</text>
   <text fill="gray" font-size="12" id="svg_28" text-anchor="middle" x="910" y="237">• 跨域协调</text>
  </g>
  <g id="vehicle-to-enterprise">
   <!-- 上行箭头 -->
   <path d="m604,604l0,-61.285717l0,-65.000002l0,-81.714289" fill="none" id="svg_29" marker-end="url(#arrowhead)" stroke="black" stroke-width="2" transform="rotate(-90 604 500)"/>
   <!-- 上行数据说明 -->
  </g>
  <g id="enterprise-to-gov">
   <!-- 上行箭头 -->
   <path d="m340,432.999997l0,-86.500001l0,-86.500001" fill="none" id="svg_36" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
   <!-- 上行数据说明 -->
   <!-- 下行箭头 -->
   <path d="m250,257.999995l0,86.000003l0,86.000003" fill="none" id="svg_45" marker-end="url(#arrowhead2)" stroke="black" stroke-dasharray="3,3" stroke-width="2"/>
   <!-- 下行数据说明 -->
  </g>
  <g id="gov-to-national">
   <!-- 横向连接线 -->
   <path d="m500.999997,187l102.999997,0l0,0l102.999997,0" fill="none" id="svg_53" marker-end="url(#arrowhead-gray)" stroke="gray" stroke-dasharray="5,5" stroke-width="2"/>
   <!-- 数据说明 -->
  </g>
  <g id="legend">
   <rect fill="white" height="131.000012" id="svg_58" stroke="black" width="349.999995" x="52" y="643"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_59" text-anchor="middle" transform="matrix(1.01786 0 0 1 -3.94644 0)" x="222.4736" y="663">[数据分类]</text>
   <text fill="black" font-size="12" font-weight="bold" id="svg_60" x="72" y="688">1. 连接管理类</text>
   <text fill="black" font-size="11" id="svg_61" x="82" y="708">登入/登出、心跳、鉴权</text>
   <text fill="black" font-size="12" font-weight="bold" id="svg_62" x="71" y="733">2. 处理流程数据</text>
   <text fill="black" font-size="11" id="svg_63" x="82" y="753">8个生命周期环节数据</text>
   <text fill="black" font-size="12" font-weight="bold" id="svg_64" x="261" y="688">3. 事件数据</text>
   <text font-size="11" id="svg_65" x="271" y="708">异常事件实时上报</text>
   <text font-size="12" font-weight="bold" id="svg_66" x="261" y="733">4. 统计数据</text>
   <text font-size="11" id="svg_67" x="271" y="753">定期统计汇总信息</text>
  </g>
  <g id="comm-mechanism">
   <rect fill="white" height="131.00001" id="svg_68" stroke="black" width="300" x="415" y="643"/>
   <text fill="black" font-size="14" font-weight="bold" id="svg_69" text-anchor="middle" x="565" y="666">[通信机制]</text>
   <text font-size="11" id="svg_70" x="435" y="691">• TCP/IP长连接</text>
   <text font-size="11" id="svg_71" x="435" y="711">• 心跳保活（默认60秒）</text>
   <text font-size="11" id="svg_72" x="435" y="731">• 数据加密传输</text>
   <text font-size="11" id="svg_73" x="435" y="751">• 断线重连与补发机制</text>
  </g>
  <g id="svg_80">
   <rect fill="white" height="60" id="svg_79" stroke="gray" stroke-dasharray="3,3" width="160" x="524" y="102"/>
   <text fill="gray" font-size="12" font-weight="bold" id="svg_78" x="534" y="122">预留接口</text>
   <text fill="gray" font-size="11" id="svg_77" x="534" y="137">• 统计汇总数据</text>
   <text fill="gray" font-size="11" id="svg_76" x="534" y="152">• 重大事件上报</text>
  </g>
  <g id="svg_87">
   <rect fill="white" height="100" id="svg_86" stroke="black" width="153.999996" x="528" y="515"/>
   <text font-size="12" font-weight="bold" id="svg_85" x="538" y="535">上行数据（0x01-0x05）</text>
   <text font-size="11" id="svg_84" x="538" y="555">• 车辆登入/登出</text>
   <text fill="black" font-size="11" id="svg_83" x="538" y="570">• 处理流程数据</text>
   <text font-size="11" id="svg_82" x="538" y="585">• 事件数据上报</text>
   <text font-size="11" id="svg_81" x="538" y="600">• 补发数据上报</text>
  </g>
  <g id="svg_96">
   <rect fill="white" height="120" id="svg_95" stroke="black" width="200" x="359" y="286"/>
   <text font-size="12" font-weight="bold" id="svg_94" x="369" y="307">上行数据（0x40-0x48）</text>
   <text font-size="11" id="svg_93" x="369" y="322">• 企业鉴权/心跳</text>
   <text fill="black" font-size="11" id="svg_92" x="369" y="337">• 处理流程数据汇总</text>
   <text fill="black" font-size="11" id="svg_91" x="369" y="352">• 事件数据汇总</text>
   <text font-size="11" id="svg_90" x="369" y="367">• 统计数据上报</text>
   <text font-size="11" id="svg_89" x="369" y="382">• 数据查询响应</text>
   <text font-size="11" id="svg_88" x="369" y="397">• 补发数据上报</text>
  </g>
  <g id="svg_104">
   <rect fill="white" height="105" id="svg_103" stroke="black" width="180" x="54" y="291.5"/>
   <text font-size="12" font-weight="bold" id="svg_102" x="64" y="311.5">下行数据</text>
   <text font-size="11" id="svg_101" x="64" y="326.5">• 平台通用应答(0x41)</text>
   <text font-size="11" id="svg_100" x="64" y="341.5">• 鉴权应答(0x44)</text>
   <text font-size="11" id="svg_99" x="64" y="356.5">• 数据查询(0x49)</text>
   <text font-size="11" id="svg_98" x="64" y="371.5">• 传输参数控制(0x80)</text>
   <text font-size="11" id="svg_97" x="64" y="386.5">• 密钥交换(0x4B)</text>
  </g>
 </g>
</svg>