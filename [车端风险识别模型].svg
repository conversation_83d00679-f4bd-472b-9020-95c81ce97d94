<svg xmlns="http://www.w3.org/2000/svg" width="900" height="300" viewBox="0 0 900 300">
  <style>
    .container { fill: white; stroke: black; stroke-width: 1; rx: 5; ry: 5; }
    .block { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 1; rx: 3; ry: 3; }
    .text { font-family: sans-serif; font-size: 13px; font-weight: bold; text-anchor: middle; }
    .subtext { font-family: sans-serif; font-size: 11px; text-anchor: middle; fill: #333; }
    .detail-text { font-family: sans-serif; font-size: 11px; fill: #333; }
    .tech-text { font-family: sans-serif; font-size: 10px; fill: #d9534f; font-style: italic; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>

  <!-- Input Block -->
  <rect x="10" y="125" width="120" height="50" class="container"/>
  <text x="70" y="145" class="text">车端实时信息</text>
  <text x="70" y="165" class="subtext">(协议0x20)</text>

  <line x="130" y="150" x2="160" y2="150" class="arrow"/>

  <!-- Processing Containers -->
  <rect x="160" y="10" width="180" height="280" class="container"/>
  <text x="250" y="30" class="text">数据收集阶段</text>

  <rect x="360" y="10" width="180" height="280" class="container"/>
  <text x="450" y="30" class="text">数据存储阶段</text>

  <rect x="560" y="10" width="180" height="280" class="container"/>
  <text x="650" y="30" class="text">数据传输阶段</text>

  <!-- Details in Containers -->
  <!-- Collection -->
  <rect x="170" y="50" width="160" height="100" class="block"/>
  <text x="250" y="70" class="detail-text" style="text-anchor: middle;">超出划定测试区域</text>
  <text x="180" y="90" class="tech-text">技术: 地理围栏(R-Tree)</text>
  <text x="180" y="105" class="tech-text">实时位置匹配</text>

  <rect x="170" y="170" width="160" height="100" class="block"/>
  <text x="250" y="190" class="detail-text" style="text-anchor: middle;">收集频率异常</text>
  <text x="180" y="210" class="tech-text">技术: 时间序列分析</text>
  <text x="180" y="225" class="tech-text">滑动窗口统计</text>

  <!-- Storage -->
  <rect x="370" y="50" width="160" height="100" class="block"/>
  <text x="450" y="70" class="detail-text" style="text-anchor: middle;">明文存储敏感数据</text>
  <text x="380" y="90" class="tech-text">技术: 协议解析</text>
  <text x="380" y="105" class="tech-text">加密状态检测</text>

  <rect x="370" y="170" width="160" height="100" class="block"/>
  <text x="450" y="190" class="detail-text" style="text-anchor: middle;">存储里程超限</text>
  <text x="380" y="210" class="tech-text">技术: 阈值预警</text>
  <text x="380" y="225" class="tech-text">容量监控</text>

  <!-- Transmission -->
  <rect x="570" y="50" width="160" height="100" class="block"/>
  <text x="650" y="70" class="detail-text" style="text-anchor: middle;">传输未合规处理坐标</text>
  <text x="580" y="90" class="tech-text">技术: 深度包检测(DPI)</text>
  <text x="580" y="105" class="tech-text">坐标识别</text>

  <rect x="570" y="170" width="160" height="100" class="block"/>
  <text x="650" y="190" class="detail-text" style="text-anchor: middle;">向未授权目的地传输</text>
  <text x="580" y="210" class="tech-text">技术: 备案比对</text>
  <text x="580" y="225" class="tech-text">IP地址识别</text>

  <!-- Arrows between containers -->
  <line x="340" y="150" x2="360" y2="150" class="arrow"/>
  <line x="540" y="150" x2="560" y2="150" class="arrow"/>
  <line x="740" y="150" x2="760" y2="150" class="arrow"/>

  <!-- Output Block -->
  <rect x="760" y="100" width="130" height="100" class="container"/>
  <text x="825" y="120" class="text">综合风险评估</text>
  <text x="825" y="140" class="subtext">多因子加权(AHP)</text>
  <text x="825" y="155" class="subtext">模糊综合评价</text>
  <text x="825" y="170" class="subtext">动态模型优化</text>
  <text x="825" y="190" class="detail-text">-> 输出风险分值</text>
</svg>