<svg xmlns="http://www.w3.org/2000/svg" width="800" height="250" viewBox="0 0 800 250">
  <style>
    .block { fill: white; stroke: black; stroke-width: 1; rx: 5; ry: 5; }
    .text { font-family: sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
    .subtext { font-family: sans-serif; font-size: 10px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>

  <!-- Data Source -->
  <rect x="10" y="120" width="100" height="60" class="block"/>
  <text x="60" y="135" class="text">数据源</text>
  <text x="60" y="155" class="subtext">车端/企业端</text>
  <text x="60" y="168" class="subtext">实时二进制流</text>

  <line x="110" y="150" x2="130" y2="150" class="arrow"/>

  <!-- Data Access -->
  <rect x="130" y="120" width="100" height="60" class="block"/>
  <text x="180" y="135" class="text">数据接入</text>
  <text x="180" y="155" class="subtext">协议解析(Netty)</text>
  <text x="180" y="168" class="subtext">身份认证</text>

  <line x="230" y="150" x2="250" y2="150" class="arrow"/>

  <!-- Message Queue -->
  <rect x="250" y="120" width="100" height="60" class="block"/>
  <text x="300" y="135" class="text">消息队列</text>
  <text x="300" y="155" class="subtext">数据缓冲(Kafka)</text>
  <text x="300" y="168" class="subtext">削峰填谷</text>

  <line x="350" y="150" x2="370" y2="150" class="arrow"/>

  <!-- Stream Processing -->
  <rect x="370" y="120" width="100" height="60" class="block"/>
  <text x="420" y="135" class="text">流式处理</text>
  <text x="420" y="155" class="subtext">数据预处理(Flink)</text>
  <text x="420" y="168" class="subtext">清洗/标准化</text>

  <line x="470" y="150" x2="490" y2="150" class="arrow"/>

  <!-- Risk Identification Engine -->
  <rect x="490" y="120" width="120" height="60" class="block"/>
  <text x="550" y="135" class="text">风险识别引擎</text>
  <text x="550" y="155" class="subtext">规则匹配(Drools)</text>
  <text x="550" y="168" class="subtext">复杂事件处理(CEP)</text>

  <line x="610" y="150" x2="630" y2="150" class="arrow"/>

  <!-- Risk Event Output -->
  <rect x="630" y="120" width="100" height="60" class="block"/>
  <text x="680" y="135" class="text">风险事件输出</text>
  <text x="680" y="155" class="subtext">分级预警</text>
  <text x="680" y="168" class="subtext">联动处置</text>

  <!-- Risk Rule Base -->
  <rect x="500" y="30" width="100" height="50" class="block"/>
  <text x="550" y="45" class="text">风险规则库</text>
  <text x="550" y="65" class="subtext">车端/企业端规则</text>

  <line x="550" y="80" x2="550" y2="120" class="arrow"/>

</svg>