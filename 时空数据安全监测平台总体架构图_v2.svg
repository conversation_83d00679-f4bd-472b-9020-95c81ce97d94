<svg width="1200" height="1000" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1200" height="1000" fill="white" stroke="black" stroke-width="2"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="SimHei, Arial" font-size="18" font-weight="bold">时空数据安全监测平台总体架构图</text>
  
  <!-- 国家监测平台（顶层简单标识） -->
  <rect x="100" y="60" width="1000" height="50" fill="#f8f8f8" stroke="black" stroke-width="2"/>
  <text x="600" y="80" text-anchor="middle" font-family="SimHei, Arial" font-size="16" font-weight="bold">国家监测平台</text>
  <text x="300" y="95" text-anchor="middle" font-family="Sim<PERSON><PERSON>, Arial" font-size="10">宏观政策制定</text>
  <text x="500" y="95" text-anchor="middle" font-family="Sim<PERSON><PERSON>, Arial" font-size="10">跨域协调管理</text>
  <text x="700" y="95" text-anchor="middle" font-family="SimHei, Arial" font-size="10">重大事件处置</text>
  <text x="900" y="95" text-anchor="middle" font-family="SimHei, Arial" font-size="10">数据统计分析</text>
  
  <!-- 连接线和数据流向标注 -->
  <line x1="600" y1="110" x2="600" y2="130" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="620" y="125" font-family="SimHei, Arial" font-size="9" fill="red">区域统计汇总↑</text>
  <text x="520" y="125" font-family="SimHei, Arial" font-size="9" fill="blue">政策指令下发↓</text>
  
  <!-- 属地监测平台框架 -->
  <rect x="50" y="140" width="1100" height="480" fill="none" stroke="black" stroke-width="3"/>
  <text x="600" y="165" text-anchor="middle" font-family="SimHei, Arial" font-size="16" font-weight="bold">属地监测平台</text>
  
  <!-- 属地监测平台内部架构 -->
  <g id="regional-platform-architecture">
    <!-- 展现交互层 -->
    <rect x="70" y="180" width="1020" height="60" fill="none" stroke="black" stroke-width="2"/>
    <text x="580" y="200" text-anchor="middle" font-family="SimHei, Arial" font-size="14" font-weight="bold">展现交互层</text>
    
    <rect x="100" y="210" width="120" height="25" fill="#f0f0f0" stroke="black" stroke-width="1"/>
    <text x="160" y="227" text-anchor="middle" font-family="SimHei, Arial" font-size="10">政府端门户</text>
    
    <rect x="240" y="210" width="120" height="25" fill="#f0f0f0" stroke="black" stroke-width="1"/>
    <text x="300" y="227" text-anchor="middle" font-family="SimHei, Arial" font-size="10">企业端门户</text>
    
    <rect x="380" y="210" width="120" height="25" fill="#f0f0f0" stroke="black" stroke-width="1"/>
    <text x="440" y="227" text-anchor="middle" font-family="SimHei, Arial" font-size="10">开放API</text>
    
    <rect x="520" y="210" width="120" height="25" fill="#f0f0f0" stroke="black" stroke-width="1"/>
    <text x="580" y="227" text-anchor="middle" font-family="SimHei, Arial" font-size="10">移动应用</text>
    
    <rect x="660" y="210" width="120" height="25" fill="#f0f0f0" stroke="black" stroke-width="1"/>
    <text x="720" y="227" text-anchor="middle" font-family="SimHei, Arial" font-size="10">可视化大屏</text>
    
    <!-- 业务应用层 -->
    <rect x="70" y="260" width="1020" height="80" fill="none" stroke="black" stroke-width="2"/>
    <text x="580" y="280" text-anchor="middle" font-family="SimHei, Arial" font-size="14" font-weight="bold">业务应用层</text>
    
    <rect x="100" y="290" width="150" height="40" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="175" y="305" text-anchor="middle" font-family="SimHei, Arial" font-size="10">备案管理</text>
    <text x="175" y="320" text-anchor="middle" font-family="SimHei, Arial" font-size="10">服务</text>
    
    <rect x="270" y="290" width="150" height="40" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="345" y="305" text-anchor="middle" font-family="SimHei, Arial" font-size="10">实时监控</text>
    <text x="345" y="320" text-anchor="middle" font-family="SimHei, Arial" font-size="10">服务</text>
    
    <rect x="440" y="290" width="150" height="40" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="515" y="305" text-anchor="middle" font-family="SimHei, Arial" font-size="10">风险预警</text>
    <text x="515" y="320" text-anchor="middle" font-family="SimHei, Arial" font-size="10">服务</text>
    
    <rect x="610" y="290" width="150" height="40" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="685" y="305" text-anchor="middle" font-family="SimHei, Arial" font-size="10">监管检查</text>
    <text x="685" y="320" text-anchor="middle" font-family="SimHei, Arial" font-size="10">服务</text>
    
    <rect x="780" y="290" width="150" height="40" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="855" y="305" text-anchor="middle" font-family="SimHei, Arial" font-size="10">溯源分析</text>
    <text x="855" y="320" text-anchor="middle" font-family="SimHei, Arial" font-size="10">服务</text>
    
    <!-- 平台服务层 -->
    <rect x="70" y="360" width="1020" height="80" fill="none" stroke="black" stroke-width="2"/>
    <text x="580" y="380" text-anchor="middle" font-family="SimHei, Arial" font-size="14" font-weight="bold">平台服务层</text>
    
    <rect x="100" y="390" width="150" height="40" fill="#d0d0d0" stroke="black" stroke-width="1"/>
    <text x="175" y="405" text-anchor="middle" font-family="SimHei, Arial" font-size="10">大数据处理</text>
    <text x="175" y="420" text-anchor="middle" font-family="SimHei, Arial" font-size="10">Flink/Spark</text>
    
    <rect x="270" y="390" width="150" height="40" fill="#d0d0d0" stroke="black" stroke-width="1"/>
    <text x="345" y="405" text-anchor="middle" font-family="SimHei, Arial" font-size="10">AI分析算法</text>
    <text x="345" y="420" text-anchor="middle" font-family="SimHei, Arial" font-size="10">ML/DL算法</text>
    
    <rect x="440" y="390" width="150" height="40" fill="#d0d0d0" stroke="black" stroke-width="1"/>
    <text x="515" y="405" text-anchor="middle" font-family="SimHei, Arial" font-size="10">GIS服务</text>
    <text x="515" y="420" text-anchor="middle" font-family="SimHei, Arial" font-size="10">空间分析</text>
    
    <rect x="610" y="390" width="150" height="40" fill="#d0d0d0" stroke="black" stroke-width="1"/>
    <text x="685" y="405" text-anchor="middle" font-family="SimHei, Arial" font-size="10">区块链服务</text>
    <text x="685" y="420" text-anchor="middle" font-family="SimHei, Arial" font-size="10">存证溯源</text>
    
    <rect x="780" y="390" width="150" height="40" fill="#d0d0d0" stroke="black" stroke-width="1"/>
    <text x="855" y="405" text-anchor="middle" font-family="SimHei, Arial" font-size="10">消息队列</text>
    <text x="855" y="420" text-anchor="middle" font-family="SimHei, Arial" font-size="10">Kafka</text>
    
    <!-- 数据资源层 -->
    <rect x="70" y="460" width="1020" height="80" fill="none" stroke="black" stroke-width="2"/>
    <text x="580" y="480" text-anchor="middle" font-family="SimHei, Arial" font-size="14" font-weight="bold">数据资源层</text>
    
    <rect x="90" y="490" width="140" height="40" fill="#c0c0c0" stroke="black" stroke-width="1"/>
    <text x="160" y="505" text-anchor="middle" font-family="SimHei, Arial" font-size="10">消息队列</text>
    <text x="160" y="520" text-anchor="middle" font-family="SimHei, Arial" font-size="10">Kafka</text>
    
    <rect x="250" y="490" width="140" height="40" fill="#c0c0c0" stroke="black" stroke-width="1"/>
    <text x="320" y="505" text-anchor="middle" font-family="SimHei, Arial" font-size="10">热数据存储</text>
    <text x="320" y="520" text-anchor="middle" font-family="SimHei, Arial" font-size="10">Doris</text>
    
    <rect x="410" y="490" width="140" height="40" fill="#c0c0c0" stroke="black" stroke-width="1"/>
    <text x="480" y="505" text-anchor="middle" font-family="SimHei, Arial" font-size="10">冷数据存储</text>
    <text x="480" y="520" text-anchor="middle" font-family="SimHei, Arial" font-size="10">MinIO</text>
    
    <rect x="570" y="490" width="140" height="40" fill="#c0c0c0" stroke="black" stroke-width="1"/>
    <text x="640" y="505" text-anchor="middle" font-family="SimHei, Arial" font-size="10">关系数据库</text>
    <text x="640" y="520" text-anchor="middle" font-family="SimHei, Arial" font-size="10">PostgreSQL</text>
    
    <rect x="730" y="490" width="140" height="40" fill="#c0c0c0" stroke="black" stroke-width="1"/>
    <text x="800" y="505" text-anchor="middle" font-family="SimHei, Arial" font-size="10">日志存储</text>
    <text x="800" y="520" text-anchor="middle" font-family="SimHei, Arial" font-size="10">ElasticSearch</text>
    
    <rect x="890" y="490" width="140" height="40" fill="#c0c0c0" stroke="black" stroke-width="1"/>
    <text x="960" y="505" text-anchor="middle" font-family="SimHei, Arial" font-size="10">缓存数据库</text>
    <text x="960" y="520" text-anchor="middle" font-family="SimHei, Arial" font-size="10">Redis</text>
    
    <!-- 基础设施层 -->
    <rect x="70" y="560" width="1020" height="50" fill="none" stroke="black" stroke-width="2"/>
    <text x="580" y="580" text-anchor="middle" font-family="SimHei, Arial" font-size="14" font-weight="bold">基础设施层</text>
    
    <rect x="120" y="590" width="180" height="15" fill="#a0a0a0" stroke="black" stroke-width="1"/>
    <text x="210" y="600" text-anchor="middle" font-family="SimHei, Arial" font-size="9">容器化平台(Docker+K8s)</text>
    
    <rect x="320" y="590" width="180" height="15" fill="#a0a0a0" stroke="black" stroke-width="1"/>
    <text x="410" y="600" text-anchor="middle" font-family="SimHei, Arial" font-size="9">负载均衡(Nginx)</text>
    
    <rect x="520" y="590" width="180" height="15" fill="#a0a0a0" stroke="black" stroke-width="1"/>
    <text x="610" y="600" text-anchor="middle" font-family="SimHei, Arial" font-size="9">核心网络(双核交换机)</text>
    
    <rect x="720" y="590" width="180" height="15" fill="#a0a0a0" stroke="black" stroke-width="1"/>
    <text x="810" y="600" text-anchor="middle" font-family="SimHei, Arial" font-size="9">安全防护(防火墙+IPS)</text>
  </g>
  
  <!-- 安全保障体系（右侧） -->
  <rect x="1120" y="180" width="25" height="430" fill="none" stroke="black" stroke-width="2"/>
  <text x="1132" y="200" text-anchor="middle" font-family="SimHei, Arial" font-size="10" font-weight="bold" transform="rotate(90 1132 200)">安全保障体系</text>
  <text x="1132" y="260" text-anchor="middle" font-family="SimHei, Arial" font-size="8" transform="rotate(90 1132 260)">PKI认证</text>
  <text x="1132" y="320" text-anchor="middle" font-family="SimHei, Arial" font-size="8" transform="rotate(90 1132 320)">国密加密</text>
  <text x="1132" y="380" text-anchor="middle" font-family="SimHei, Arial" font-size="8" transform="rotate(90 1132 380)">RBAC权限</text>
  <text x="1132" y="440" text-anchor="middle" font-family="SimHei, Arial" font-size="8" transform="rotate(90 1132 440)">审计追踪</text>
  <text x="1132" y="500" text-anchor="middle" font-family="SimHei, Arial" font-size="8" transform="rotate(90 1132 500)">容灾备份</text>
  
  <!-- 属地平台与通信协议连接 -->
  <line x1="600" y1="620" x2="600" y2="640" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="620" y="635" font-family="SimHei, Arial" font-size="9" fill="red">监管数据↑</text>
  <text x="530" y="635" font-family="SimHei, Arial" font-size="9" fill="blue">指令下达↓</text>
  
  <!-- 分层通信协议体系（中间位置） -->
  <g id="communication-protocol">
    <rect x="400" y="650" width="400" height="80" fill="#f8f8f8" stroke="black" stroke-width="2"/>
    <text x="600" y="670" text-anchor="middle" font-family="SimHei, Arial" font-size="14" font-weight="bold">分层通信协议体系</text>
    
    <rect x="420" y="680" width="360" height="15" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="600" y="690" text-anchor="middle" font-family="SimHei, Arial" font-size="9">应用协议层：基于《时空数据安全监测平台通信协议规范》的自定义二进制协议</text>
    
    <rect x="420" y="695" width="360" height="15" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="600" y="705" text-anchor="middle" font-family="SimHei, Arial" font-size="9">安全传输层：国密SSL/TLS 1.3协议，SM2/SM4加密算法</text>
    
    <rect x="420" y="710" width="360" height="15" fill="#e8e8e8" stroke="black" stroke-width="1"/>
    <text x="600" y="720" text-anchor="middle" font-family="SimHei, Arial" font-size="9">网络基础层：TCP/IP双栈协议，支持IPv4/IPv6</text>
  </g>
  
  <!-- 通信协议与企业平台连接 -->
  <line x1="600" y1="730" x2="600" y2="750" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="620" y="745" font-family="SimHei, Arial" font-size="9" fill="red">企业数据↑</text>
  <text x="530" y="745" font-family="SimHei, Arial" font-size="9" fill="blue">配置指令↓</text>
  
  <!-- 企业平台 -->
  <rect x="100" y="760" width="1000" height="80" fill="#f0f0f0" stroke="black" stroke-width="2"/>
  <text x="600" y="780" text-anchor="middle" font-family="SimHei, Arial" font-size="16" font-weight="bold">企业平台</text>
  
  <!-- 企业平台功能模块 -->
  <rect x="150" y="800" width="120" height="30" fill="#e0e0e0" stroke="black" stroke-width="1"/>
  <text x="210" y="820" text-anchor="middle" font-family="SimHei, Arial" font-size="10">数据汇聚</text>
  
  <rect x="290" y="800" width="120" height="30" fill="#e0e0e0" stroke="black" stroke-width="1"/>
  <text x="350" y="820" text-anchor="middle" font-family="SimHei, Arial" font-size="10">合规自查</text>
  
  <rect x="430" y="800" width="120" height="30" fill="#e0e0e0" stroke="black" stroke-width="1"/>
  <text x="490" y="820" text-anchor="middle" font-family="SimHei, Arial" font-size="10">安全防护</text>
  
  <rect x="570" y="800" width="120" height="30" fill="#e0e0e0" stroke="black" stroke-width="1"/>
  <text x="630" y="820" text-anchor="middle" font-family="SimHei, Arial" font-size="10">数据上报</text>
  
  <rect x="710" y="800" width="120" height="30" fill="#e0e0e0" stroke="black" stroke-width="1"/>
  <text x="770" y="820" text-anchor="middle" font-family="SimHei, Arial" font-size="10">终端管理</text>
  
  <rect x="850" y="800" width="120" height="30" fill="#e0e0e0" stroke="black" stroke-width="1"/>
  <text x="910" y="820" text-anchor="middle" font-family="SimHei, Arial" font-size="10">应急响应</text>
  
  <!-- 企业平台与终端节点连接 -->
  <line x1="600" y1="840" x2="600" y2="860" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="620" y="855" font-family="SimHei, Arial" font-size="9" fill="red">终端数据↑</text>
  <text x="530" y="855" font-family="SimHei, Arial" font-size="9" fill="blue">参数配置↓</text>
  
  <!-- 终端节点层 -->
  <rect x="100" y="870" width="1000" height="60" fill="#e8e8e8" stroke="black" stroke-width="2"/>
  <text x="600" y="890" text-anchor="middle" font-family="SimHei, Arial" font-size="16" font-weight="bold">终端节点</text>
  
  <!-- 终端节点功能模块 -->
  <rect x="180" y="905" width="120" height="20" fill="#d8d8d8" stroke="black" stroke-width="1"/>
  <text x="240" y="918" text-anchor="middle" font-family="SimHei, Arial" font-size="10">车载终端</text>
  
  <rect x="320" y="905" width="120" height="20" fill="#d8d8d8" stroke="black" stroke-width="1"/>
  <text x="380" y="918" text-anchor="middle" font-family="SimHei, Arial" font-size="10">路侧设备</text>
  
  <rect x="460" y="905" width="120" height="20" fill="#d8d8d8" stroke="black" stroke-width="1"/>
  <text x="520" y="918" text-anchor="middle" font-family="SimHei, Arial" font-size="10">数据采集</text>
  
  <rect x="600" y="905" width="120" height="20" fill="#d8d8d8" stroke="black" stroke-width="1"/>
  <text x="660" y="918" text-anchor="middle" font-family="SimHei, Arial" font-size="10">状态监控</text>
  
  <rect x="740" y="905" width="120" height="20" fill="#d8d8d8" stroke="black" stroke-width="1"/>
  <text x="800" y="918" text-anchor="middle" font-family="SimHei, Arial" font-size="10">边缘计算</text>
  
  <!-- 性能指标标注 -->
  <text x="200" y="50" text-anchor="middle" font-family="SimHei, Arial" font-size="10" fill="blue" font-weight="bold">支持50万+车辆</text>
  <text x="400" y="50" text-anchor="middle" font-family="SimHei, Arial" font-size="10" fill="blue" font-weight="bold">10万+并发连接</text>
  <text x="600" y="50" text-anchor="middle" font-family="SimHei, Arial" font-size="10" fill="blue" font-weight="bold">PB级数据处理</text>
  <text x="800" y="50" text-anchor="middle" font-family="SimHei, Arial" font-size="10" fill="blue" font-weight="bold">毫秒级响应</text>
  <text x="1000" y="50" text-anchor="middle" font-family="SimHei, Arial" font-size="10" fill="blue" font-weight="bold">99.9%可用性</text>
  
  <!-- 技术特点 -->
  <rect x="50" y="950" width="1100" height="90" fill="none" stroke="black" stroke-width="2"/>
  <text x="600" y="970" text-anchor="middle" font-family="SimHei, Arial" font-size="16" font-weight="bold">技术特点</text>
  
  <!-- 技术特点内容 -->
  <text x="70" y="990" font-family="SimHei, Arial" font-size="11">• 四级分布式架构：国家-属地-企业-终端协同监管，数据分级存储计算</text>
  <text x="70" y="1005" font-family="SimHei, Arial" font-size="11">• 标准化通信协议：基于国家标准的分层协议体系，支持多协议接入</text>
  <text x="70" y="1020" font-family="SimHei, Arial" font-size="11">• 高性能实时处理：微服务架构+流式计算，支持50万+车辆并发接入</text>
  
  <text x="620" y="990" font-family="SimHei, Arial" font-size="11">• 智能风险识别：规则引擎+AI模型双驱动，毫秒级风险预警</text>
  <text x="620" y="1005" font-family="SimHei, Arial" font-size="11">• 全链路数据溯源：完整血缘关系+审计追踪，支持责任认定</text>
  <text x="620" y="1020" font-family="SimHei, Arial" font-size="11">• 全方位安全保障：国密算法+区块链存证+多重认证防护</text>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>
</svg>