<?xml version="1.0"?>
<svg width="1200" height="600" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
 <!-- 标题 -->
 <!-- 主数据包结构 -->
 <!-- 命令单元详细分解 -->
 <!-- 识别码详细分解 -->
 <!-- 校验范围标识 -->
 <!-- 组包机制说明 -->
 <!-- 主要命令范围标识 -->
 <g class="layer">
  <title>Layer 1</title>
  <g id="svg_2">
   <!-- 起始符 -->
   <!-- 数据包数量 - 新增字段 -->
   <!-- 命令单元 -->
   <!-- 识别码 -->
   <!-- 时间戳 -->
   <!-- 消息ID -->
   <!-- 加密方式 -->
   <!-- 数据长度 -->
   <!-- 数据单元 -->
   <!-- 校验码 -->
  </g>
  <g id="svg_33" transform="translate(50, 150)"/>
  <g id="svg_41"/>
  <g id="svg_49"/>
  <g id="svg_53"/>
  <g id="svg_58"/>
  <g id="svg_63">
   <g id="svg_64">
    <text fill="#333" font-family="Arial, sans-serif" font-size="18" font-weight="bold" id="svg_1" text-anchor="middle" x="582" y="155">时空数据安全监测平台二进制数据包结构图</text>
    <rect fill="#e8f4fd" height="50" id="svg_3" stroke="#333" width="80" x="154" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_4" text-anchor="middle" x="194" y="204">起始符</text>
    <text font-size="10" id="svg_5" text-anchor="middle" x="194" y="219">(2B)</text>
    <rect fill="#fff2cc" height="50" id="svg_6" stroke="#333" width="80" x="234" y="184"/>
    <text fill="black" font-size="11" font-weight="bold" id="svg_7" text-anchor="middle" x="274" y="204">数据包数量</text>
    <text fill="black" font-size="10" id="svg_8" text-anchor="middle" x="273" y="221">(2B)</text>
    <rect fill="#f8cecc" height="50" id="svg_9" stroke="#333" width="80" x="314" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_10" text-anchor="middle" x="354" y="204">命令单元</text>
    <text font-size="10" id="svg_11" text-anchor="middle" x="354" y="219">(2B)</text>
    <rect fill="#d5e8d4" height="50" id="svg_12" stroke="#333" width="140" x="394" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_13" text-anchor="middle" x="464" y="204">识别码</text>
    <text font-size="10" id="svg_14" text-anchor="middle" x="464" y="219">(29B)</text>
    <rect fill="#e1d5e7" height="50" id="svg_15" stroke="#333" width="90" x="534" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_16" text-anchor="middle" x="579" y="204">时间戳</text>
    <text font-size="10" id="svg_17" text-anchor="middle" x="579" y="219">(13B)</text>
    <rect fill="#fff2cc" height="50" id="svg_18" stroke="#333" width="80" x="624" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_19" text-anchor="middle" x="664" y="204">消息ID</text>
    <text font-size="10" id="svg_20" text-anchor="middle" x="664" y="219">(9B)</text>
    <rect fill="#f8cecc" height="50" id="svg_21" stroke="#333" width="80" x="704" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_22" text-anchor="middle" x="744" y="204">加密方式</text>
    <text font-size="10" id="svg_23" text-anchor="middle" x="744" y="219">(1B)</text>
    <rect fill="#e8f4fd" height="50" id="svg_24" stroke="#333" width="80" x="784" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_25" text-anchor="middle" x="824" y="204">数据长度</text>
    <text font-size="10" id="svg_26" text-anchor="middle" x="824" y="219">(2B)</text>
    <rect fill="#d5e8d4" height="50" id="svg_27" stroke="#333" width="120" x="864" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_28" text-anchor="middle" x="924" y="204">数据单元</text>
    <text font-size="10" id="svg_29" text-anchor="middle" x="924" y="219">(变长)</text>
    <rect fill="#e1d5e7" height="50" id="svg_30" stroke="#333" width="80" x="984" y="184"/>
    <text font-size="11" font-weight="bold" id="svg_31" text-anchor="middle" x="1024" y="204">校验码</text>
    <text font-size="10" id="svg_32" text-anchor="middle" x="1024" y="219">(1B)</text>
    <text fill="#333" font-size="14" font-weight="bold" id="svg_34" x="156" y="295">命令单元详细结构：</text>
    <rect fill="#f8cecc" height="40" id="svg_35" stroke="#333" width="80" x="156" y="305"/>
    <text font-size="11" font-weight="bold" id="svg_36" text-anchor="middle" x="196" y="320">命令标识</text>
    <text font-size="10" id="svg_37" text-anchor="middle" x="196" y="333">(1B)</text>
    <rect fill="#f8cecc" height="40" id="svg_38" stroke="#333" width="80" x="236" y="305"/>
    <text fill="black" font-size="11" font-weight="bold" id="svg_39" text-anchor="middle" x="276" y="320">应答标志</text>
    <text font-size="10" id="svg_40" text-anchor="middle" x="276" y="333">(1B)</text>
    <text fill="#333" font-size="14" font-weight="bold" id="svg_42" x="370" y="295">识别码详细结构：</text>
    <rect fill="#d5e8d4" height="40" id="svg_43" stroke="#333" width="100" x="370" y="305"/>
    <text font-size="11" font-weight="bold" id="svg_44" text-anchor="middle" x="420" y="320">企业ID</text>
    <text font-size="10" id="svg_45" text-anchor="middle" x="420" y="333">(12B)</text>
    <rect fill="#d5e8d4" height="40" id="svg_46" stroke="#333" width="120" x="470" y="305"/>
    <text font-size="11" font-weight="bold" id="svg_47" text-anchor="middle" x="530" y="320">车辆识别代号(VIN)</text>
    <text font-size="10" id="svg_48" text-anchor="middle" x="530" y="333">(17B)</text>
    <text fill="#333" font-size="14" font-weight="bold" id="svg_50" x="758" y="300">BCC异或校验范围：</text>
    <rect fill="none" height="30" id="svg_51" stroke="#ff6b6b" stroke-dasharray="5,5" stroke-width="2" width="299.000001" x="758" y="310"/>
    <text fill="#d63031" font-size="11" id="svg_52" x="768" y="330">从命令单元至数据单元末尾（校验结果存储在校验码字段）</text>
    <text fill="#333" font-size="14" font-weight="bold" id="svg_54" x="152" y="396">组包发送机制：</text>
    <text fill="#666" font-size="12" id="svg_55" x="152" y="416">• 数据包数量：1~1000条，支持批量发送</text>
    <text fill="#666" font-size="12" id="svg_56" x="152" y="431">• 单条发送时数据包数量=1，保持向后兼容</text>
    <text fill="#666" font-size="12" id="svg_57" x="152" y="446">• 批量发送时数据单元包含多条记录，每条记录前2字节为长度</text>
    <text fill="#333" font-size="14" font-weight="bold" id="svg_59" x="507" y="396">命令标识范围：</text>
    <text fill="#666" font-size="12" id="svg_60" x="507" y="416">车端命令: 0x01-0x3F  |  企业平台命令: 0x40-0x7F  |  平台交互: 0x80-0xFE</text>
    <text fill="#666" font-size="12" id="svg_61" x="507" y="431">主要命令: 登入/登出(0x01/0x02), 处理流程数据上报(0x03/0x45), 事件数据上报(0x04/0x46)</text>
    <text fill="#666" font-size="12" id="svg_62" x="507" y="446">应答标志: 0x01(成功), 0x02(错误), 0x03(ID重复), 0x04(ID不存在), 0x05(鉴权错误), 0xFE(命令)</text>
   </g>
  </g>
 </g>
</svg>