<svg width="800" height="700" viewBox="0 0 800 700" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .box { fill: white; stroke: black; stroke-width: 2; }
      .blockchain { fill: #f5f5f5; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; }
      .title { font-size: 16px; font-weight: bold; }
      .subtitle { font-size: 12px; fill: #333; }
      .arrow-up { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .arrow-down { fill: none; stroke: black; stroke-width: 2; marker-end: url(#arrowhead); }
      .arrow-blockchain { fill: none; stroke: black; stroke-width: 2; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
      .label { font-size: 11px; fill: #555; }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="400" y="30" class="text title">时空数据安全信息数据监控数据流与控制流图</text>

  <!-- 四级平台架构 -->
  <!-- 国家平台 -->
  <g id="national">
    <rect x="250" y="80" width="300" height="70" class="box" rx="10"/>
    <text x="400" y="120" class="text title">国家平台</text>
  </g>

  <!-- 地方平台 -->
  <g id="local">
    <rect x="250" y="220" width="300" height="70" class="box" rx="10"/>
    <text x="400" y="260" class="text title">地方平台</text>
  </g>

  <!-- 企业平台 -->
  <g id="enterprise">
    <rect x="250" y="360" width="300" height="70" class="box" rx="10"/>
    <text x="400" y="400" class="text title">企业平台</text>
  </g>

  <!-- 车载终端 -->
  <g id="vehicle">
    <rect x="250" y="500" width="300" height="70" class="box" rx="10"/>
    <text x="400" y="540" class="text title">车载终端</text>
  </g>

  <!-- 区块链 -->
  <g id="blockchain">
    <rect x="620" y="290" width="140" height="70" class="blockchain" rx="10"/>
    <text x="690" y="325" class="text title">区块链</text>
  </g>

  <!-- 数据流（上行） - 直线 -->
  <!-- 车载到企业 -->
  <path d="M 350 500 L 350 430" class="arrow-up"/>
  <text x="320" y="470" class="text label" text-anchor="end">数据上传</text>

  <!-- 企业到地方 -->
  <path d="M 350 360 L 350 290" class="arrow-up"/>
  <text x="320" y="325" class="text label" text-anchor="end">备案申请/日志数据/整改反馈</text>

  <!-- 地方到国家 -->
  <path d="M 350 220 L 350 150" class="arrow-up"/>
  <text x="320" y="185" class="text label" text-anchor="end">备案信息/统计数据/风险报告</text>

  <!-- 控制流（下行） - 直线 -->
  <!-- 国家到地方 -->
  <path d="M 450 150 L 450 220" class="arrow-down"/>
  <text x="480" y="185" class="text label" text-anchor="start">指令/查询/政策</text>

  <!-- 地方到企业 -->
  <path d="M 450 290 L 450 360" class="arrow-down"/>
  <text x="480" y="325" class="text label" text-anchor="start">备案结果/风险工单/检查通知/指令</text>

  <!-- 企业到车载 -->
  <path d="M 450 430 L 450 500" class="arrow-down"/>
  <text x="480" y="470" class="text label" text-anchor="start">控制指令/配置更新</text>

  <!-- 区块链交互 - 折线虚线 -->
  <!-- 地方平台到区块链 -->
  <path d="M 550 255 L 580 255 L 580 310 L 620 310" class="arrow-blockchain"/>
  <text x="570" y="280" class="text label" text-anchor="start">元数据存储</text>

  <!-- 企业平台到区块链 -->
  <path d="M 550 395 L 580 395 L 580 340 L 620 340" class="arrow-blockchain"/>
  <text x="560" y="370" class="text label" text-anchor="start">关键操作日志</text>

  <!-- 图例 -->
  <g transform="translate(50, 620)">
    <rect x="0" y="0" width="250" height="60" fill="none" stroke="black" stroke-width="1"/>
    <text x="10" y="20" class="text subtitle" text-anchor="start">图例：</text>
    
    <!-- 数据流 -->
    <path d="M 20 35 L 50 35" class="arrow-up" stroke-width="2"/>
    <text x="60" y="40" class="text subtitle" text-anchor="start">数据流（上行）</text>
    
    <!-- 控制流 -->
    <path d="M 140 35 L 170 35" class="arrow-down" stroke-width="2"/>
    <text x="180" y="40" class="text subtitle" text-anchor="start">控制流（下行）</text>
    
    <!-- 区块链交互 -->
    <path d="M 20 50 L 50 50" class="arrow-blockchain" stroke-width="2"/>
    <text x="60" y="55" class="text subtitle" text-anchor="start">区块链交互</text>
  </g>

  <!-- 关键说明 -->
  <g transform="translate(320, 620)">
    <rect x="0" y="0" width="430" height="60" fill="none" stroke="black" stroke-width="1"/>
    <text x="10" y="20" class="text subtitle" text-anchor="start">系统特点：</text>
    <text x="10" y="40" class="text subtitle" text-anchor="start">• 四级架构：国家-地方-企业-车载终端</text>
    <text x="10" y="55" class="text subtitle" text-anchor="start">• 双向数据流：上行监测数据，下行控制指令</text>
    <text x="220" y="40" class="text subtitle" text-anchor="start">• 区块链存证：关键操作和元数据上链</text>
    <text x="220" y="55" class="text subtitle" text-anchor="start">• 实时监控：全生命周期数据安全监测</text>
  </g>

</svg>