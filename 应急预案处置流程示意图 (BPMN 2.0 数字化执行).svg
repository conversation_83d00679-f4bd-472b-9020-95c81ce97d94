<svg xmlns="http://www.w3.org/2000/svg" width="700" height="400" viewBox="0 0 700 400">
  <style>
    .start-end { fill: #28a745; stroke: #1e7e34; stroke-width: 2; rx: 20; ry: 20; }
    .process { fill: #007bff; stroke: #0056b3; stroke-width: 2; rx: 5; ry: 5; }
    .decision { fill: #ffc107; stroke: #e0a800; stroke-width: 2; }
    .text { font-family: sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: white; }
    .decision-text { fill: black; }
    .arrow { stroke: #343a40; stroke-width: 2; marker-end: url(#arrowhead); }
    .label { font-family: sans-serif; font-size: 11px; fill: #495057; text-anchor: middle; }
    .title { font-family: sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#343a40" />
    </marker>
  </defs>

  <text x="350" y="30" class="title">应急预案处置流程示意图 (BPMN 2.0 数字化执行)</text>

  <!-- Start -->
  <rect x="300" y="60" width="100" height="40" class="start-end"/>
  <text x="350" y="80" class="text">事件检测与告警</text>

  <line x="350" y="100" x2="350" y2="120" class="arrow"/>

  <!-- 1. 事件评估与分级 -->
  <rect x="275" y="120" width="150" height="40" class="process"/>
  <text x="350" y="140" class="text">1. 事件评估与分级</text>

  <line x="350" y="160" x2="350" y2="180" class="arrow"/>

  <!-- 2. 预案匹配与激活 -->
  <rect x="275" y="180" width="150" height="40" class="process"/>
  <text x="350" y="200" class="text">2. 预案匹配与激活</text>

  <line x="350" y="220" x2="350" y2="250" class="arrow"/>

  <!-- 3. 应急处置执行 (Parallel Gateway) -->
  <g transform="translate(350, 270)">
    <g transform="rotate(45)">
        <rect x="-20" y="-20" width="40" height="40" class="decision"/>
    </g>
    <text x="0" y="5" class="decision-text" style="font-size: 24px;">+</text>
  </g>

  <text x="350" y="305" class="label">3. 应急处置执行</text>

  <!-- Parallel Tasks -->
  <!-- Adjusting lines to connect from the gateway sides/bottom -->
  <line x="350" y="290" x2="200" y2="330" class="arrow"/>
  <line x="350" y="290" x2="350" y2="330" class="arrow"/>
  <line x="350" y="290" x2="500" y2="330" class="arrow"/>

  <rect x="150" y="330" width="100" height="40" class="process"/>
  <text x="200" y="350" class="text">3.1 快速止损</text>

  <rect x="300" y="330" width="100" height="40" class="process"/>
  <text x="350" y="350" class="text">3.2 证据固定</text>

  <rect x="450" y="330" width="100" height="40" class="process"/>
  <text x="500" y="350" class="text">3.3 系统恢复</text>

  <!-- 4. 评估与结案 (Connects back) -->
  <line x="200" y="370" x2="100" y2="350" class="arrow"/>
  <line x="350" y="370" x2="100" y2="270" class="arrow"/>
  <line x="500" y="370" x2="100" y2="190" class="arrow"/>

  <rect x="20" y="170" width="100" height="40" class="process"/>
  <text x="70" y="190" class="text">4. 评估与结案</text>

  <line x="70" y="170" x2="70" y2="130" class="arrow"/>

  <!-- End -->
  <rect x="20" y="90" width="100" height="40" class="start-end" style="fill: #dc3545; stroke: #c82333;"/>
  <text x="70" y="110" class="text">事件结束</text>
</svg>