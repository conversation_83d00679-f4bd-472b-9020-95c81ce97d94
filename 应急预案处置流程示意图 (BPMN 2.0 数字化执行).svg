<svg xmlns="http://www.w3.org/2000/svg" width="1000" height="600" viewBox="0 0 1000 600">
  <style>
    .start-end { fill: #ffffff; stroke: #000000; stroke-width: 2; rx: 20; ry: 20; }
    .process { fill: #f8f9fa; stroke: #000000; stroke-width: 2; rx: 5; ry: 5; }
    .decision { fill: #ffffff; stroke: #000000; stroke-width: 2; }
    .text { font-family: sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #000000; }
    .decision-text { fill: #000000; }
    .arrow { stroke: #000000; stroke-width: 2; marker-end: url(#arrowhead); }
    .label { font-family: sans-serif; font-size: 11px; fill: #333333; text-anchor: middle; }
    .title { font-family: sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; fill: #000000; }
    .end-event { fill: #e9ecef; stroke: #000000; stroke-width: 2; rx: 20; ry: 20; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#000000" />
    </marker>
  </defs>

  <text x="500" y="30" class="title">应急预案处置流程示意图 (BPMN 2.0 数字化执行)</text>

  <!-- Start Event -->
  <ellipse cx="500" cy="80" rx="50" ry="20" class="start-end"/>
  <text x="500" y="85" class="text">事件检测与告警</text>

  <!-- 事件检测与告警 -> 事件评估与分级 -->
  <line x1="500" y1="100" x2="500" y2="130" class="arrow"/>

  <!-- 1. 事件评估与分级 -->
  <rect x="425" y="130" width="150" height="40" class="process"/>
  <text x="500" y="150" class="text">1. 事件评估与分级</text>

  <!-- 事件评估与分级 -> 预案匹配与激活 -->
  <line x1="500" y1="170" x2="500" y2="200" class="arrow"/>

  <!-- 2. 预案匹配与激活 -->
  <rect x="425" y="200" width="150" height="40" class="process"/>
  <text x="500" y="220" class="text">2. 预案匹配与激活</text>

  <!-- 预案匹配与激活 -> 应急处置执行 (分叉网关) -->
  <line x1="500" y1="240" x2="500" y2="280" class="arrow"/>

  <!-- 3. 应急处置执行 (Parallel Gateway - 分叉) -->
  <g transform="translate(500, 300)">
    <g transform="rotate(45)">
        <rect x="-15" y="-15" width="30" height="30" class="decision"/>
    </g>
    <text x="0" y="5" class="decision-text" style="font-size: 20px; font-weight: bold;">+</text>
  </g>

  <text x="500" y="330" class="label">3. 应急处置执行</text>

  <!-- 分叉网关 -> 并行任务 -->
  <line x1="500" y1="315" x2="250" y2="380" class="arrow"/>
  <line x1="500" y1="315" x2="500" y2="380" class="arrow"/>
  <line x1="500" y1="315" x2="750" y2="380" class="arrow"/>

  <!-- 并行任务 -->
  <rect x="175" y="380" width="150" height="40" class="process"/>
  <text x="250" y="400" class="text">3.1 快速止损</text>

  <rect x="425" y="380" width="150" height="40" class="process"/>
  <text x="500" y="400" class="text">3.2 证据固定</text>

  <rect x="675" y="380" width="150" height="40" class="process"/>
  <text x="750" y="400" class="text">3.3 系统恢复</text>

  <!-- 并行任务 -> 合并网关 -->
  <line x1="250" y1="420" x2="500" y2="480" class="arrow"/>
  <line x1="500" y1="420" x2="500" y2="480" class="arrow"/>
  <line x1="750" y1="420" x2="500" y2="480" class="arrow"/>

  <!-- 合并网关 (Parallel Gateway - 合并) -->
  <g transform="translate(500, 500)">
    <g transform="rotate(45)">
        <rect x="-15" y="-15" width="30" height="30" class="decision"/>
    </g>
    <text x="0" y="5" class="decision-text" style="font-size: 20px; font-weight: bold;">+</text>
  </g>

  <!-- 合并网关 -> 评估与结案 -->
  <line x1="500" y1="515" x2="500" y2="540" class="arrow"/>

  <!-- 4. 评估与结案 -->
  <rect x="425" y="540" width="150" height="40" class="process"/>
  <text x="500" y="560" class="text">4. 评估与结案</text>

  <!-- 评估与结案 -> 事件结束 -->
  <line x1="500" y1="580" x2="500" y2="610" class="arrow"/>

  <!-- End Event -->
  <ellipse cx="500" cy="630" rx="50" ry="20" class="end-event"/>
  <text x="500" y="635" class="text">事件结束</text>
</svg>