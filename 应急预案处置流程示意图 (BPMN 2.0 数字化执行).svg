<svg xmlns="http://www.w3.org/2000/svg" width="800" height="680" viewBox="0 0 800 680">
  <style>
    .start-end { fill: #ffffff; stroke: #000000; stroke-width: 2; rx: 20; ry: 20; }
    .process { fill: #f8f9fa; stroke: #000000; stroke-width: 2; rx: 5; ry: 5; }
    .decision { fill: #ffffff; stroke: #000000; stroke-width: 2; }
    .text { font-family: sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #000000; }
    .decision-text { fill: #000000; }
    .arrow { stroke: #000000; stroke-width: 1.5; marker-end: url(#arrowhead); }
    .label { font-family: sans-serif; font-size: 10px; fill: #333333; text-anchor: middle; }
    .title { font-family: sans-serif; font-size: 15px; font-weight: bold; text-anchor: middle; fill: #000000; }
    .end-event { fill: #e9ecef; stroke: #000000; stroke-width: 2; rx: 20; ry: 20; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#000000" />
    </marker>
  </defs>

  <text x="400" y="25" class="title">应急预案处置流程示意图 (BPMN 2.0 数字化执行)</text>

  <!-- Start Event -->
  <ellipse cx="400" cy="60" rx="50" ry="20" class="start-end"/>
  <text x="400" y="65" class="text">事件检测与告警</text>

  <!-- 事件检测与告警 -> 事件评估与分级 -->
  <line x1="400" y1="80" x2="400" y2="100" class="arrow"/>

  <!-- 1. 事件评估与分级 -->
  <rect x="325" y="100" width="150" height="40" class="process"/>
  <text x="400" y="120" class="text">1. 事件评估与分级</text>

  <!-- 事件评估与分级 -> 预案匹配与激活 -->
  <line x1="400" y1="140" x2="400" y2="160" class="arrow"/>

  <!-- 2. 预案匹配与激活 -->
  <rect x="325" y="160" width="150" height="40" class="process"/>
  <text x="400" y="180" class="text">2. 预案匹配与激活</text>

  <!-- 预案匹配与激活 -> 应急处置执行 (分叉网关) -->
  <line x1="400" y1="200" x2="400" y2="230" class="arrow"/>

  <!-- 3. 应急处置执行 (Parallel Gateway - 分叉) -->
  <g transform="translate(400, 250)">
    <g transform="rotate(45)">
        <rect x="-15" y="-15" width="30" height="30" class="decision"/>
    </g>
    <text x="-5" y="5" class="decision-text" style="font-size: 18px; font-weight: bold;">+</text>
  </g>

  <text x="400" y="275" class="label">3. 应急处置执行</text>

  <!-- 分叉网关 -> 并行任务 -->
  <line x1="380" y1="250" x2="200" y2="320" class="arrow"/>
  <line x1="400" y1="270" x2="400" y2="320" class="arrow"/>
  <line x1="420" y1="250" x2="600" y2="320" class="arrow"/>

  <!-- 并行任务 -->
  <rect x="125" y="320" width="150" height="40" class="process"/>
  <text x="200" y="340" class="text">3.1 通知下达</text>

  <rect x="325" y="320" width="150" height="40" class="process"/>
  <text x="400" y="340" class="text">3.2 证据固定</text>

  <rect x="525" y="320" width="150" height="40" class="process"/>
  <text x="600" y="340" class="text">3.3 闭环管理</text>

  <!-- 并行任务 -> 合并网关 -->
  <line x1="200" y1="360" x2="380" y2="410" class="arrow"/>
  <line x1="400" y1="360" x2="400" y2="410" class="arrow"/>
  <line x1="600" y1="360" x2="420" y2="410" class="arrow"/>

  <!-- 合并网关 (Parallel Gateway - 合并) -->
  <g transform="translate(400, 430)">
    <g transform="rotate(45)">
        <rect x="-15" y="-15" width="30" height="30" class="decision"/>
    </g>
    <text x="-5" y="5" class="decision-text" style="font-size: 18px; font-weight: bold;">+</text>
  </g>

  <!-- 合并网关 -> 评估与结案 -->
  <line x1="400" y1="450" x2="400" y2="480" class="arrow"/>

  <!-- 4. 评估与结案 -->
  <rect x="325" y="480" width="150" height="40" class="process"/>
  <text x="400" y="500" class="text">4. 评估与结案</text>

  <!-- 评估与结案 -> 事件结束 -->
  <line x1="400" y1="520" x2="400" y2="550" class="arrow"/>

  <!-- End Event -->
  <ellipse cx="400" cy="570" rx="50" ry="20" class="end-event"/>
  <text x="400" y="575" class="text">事件结束</text>
</svg>