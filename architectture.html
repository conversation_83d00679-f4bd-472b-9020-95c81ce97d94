<!DOCTYPE html>
<html>
<head>
  <style>
    svg {
      width: 100%;
      height: auto;
    }
  </style>
</head>
<body>
  <svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
    <!-- 背景 -->
    <rect x="0" y="0" width="1000" height="800" fill="white"/>
    
    <!-- 标题 -->
    <text x="500" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="black">时空数据安全监测平台总体架构图</text>
    
    <!-- 左侧：四级监管架构 -->
    <g id="supervision-levels">
      <rect x="30" y="60" width="200" height="700" fill="white" stroke="black" stroke-width="2" rx="5"/>
      <text x="130" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="black">四级监管架构</text>
      
      <!-- 国家级 -->
      <rect x="50" y="110" width="160" height="80" fill="#f0f0f0" stroke="black" stroke-width="1.5" rx="3"/>
      <text x="130" y="140" text-anchor="middle" font-size="14" font-weight="bold" fill="black">国家监测平台</text>
      <text x="130" y="160" text-anchor="middle" font-size="12" fill="black">统一标准制定</text>
      <text x="130" y="175" text-anchor="middle" font-size="12" fill="black">全局态势感知</text>
      
      <!-- 属地级 -->
      <rect x="50" y="220" width="160" height="80" fill="#e0e0e0" stroke="black" stroke-width="1.5" rx="3"/>
      <text x="130" y="250" text-anchor="middle" font-size="14" font-weight="bold" fill="black">属地监测平台</text>
      <text x="130" y="270" text-anchor="middle" font-size="12" fill="black">区域监管</text>
      <text x="130" y="285" text-anchor="middle" font-size="12" fill="black">风险预警</text>
      
      <!-- 企业级 -->
      <rect x="50" y="330" width="160" height="80" fill="#d0d0d0" stroke="black" stroke-width="1.5" rx="3"/>
      <text x="130" y="360" text-anchor="middle" font-size="14" font-weight="bold" fill="black">企业平台</text>
      <text x="130" y="380" text-anchor="middle" font-size="12" fill="black">数据汇聚</text>
      <text x="130" y="395" text-anchor="middle" font-size="12" fill="black">合规自查</text>
      
      <!-- 终端级 -->
      <rect x="50" y="440" width="160" height="80" fill="#c0c0c0" stroke="black" stroke-width="1.5" rx="3"/>
      <text x="130" y="470" text-anchor="middle" font-size="14" font-weight="bold" fill="black">终端节点</text>
      <text x="130" y="490" text-anchor="middle" font-size="12" fill="black">车载终端</text>
      <text x="130" y="505" text-anchor="middle" font-size="12" fill="black">路侧设备</text>
      
      <!-- 连接线 -->
      <polyline points="130,190 130,210 130,220" stroke="black" stroke-width="2" fill="none" marker-end="url(#arrow-down)"/>
      <polyline points="130,300 130,320 130,330" stroke="black" stroke-width="2" fill="none" marker-end="url(#arrow-down)"/>
      <polyline points="130,410 130,430 130,440" stroke="black" stroke-width="2" fill="none" marker-end="url(#arrow-down)"/>
    </g>
    
    <!-- 右侧：六层技术架构 -->
    <g id="tech-layers">
      <!-- 展现交互层 -->
      <rect x="280" y="60" width="680" height="80" fill="white" stroke="black" stroke-width="2" rx="5"/>
      <text x="620" y="85" text-anchor="middle" font-size="15" font-weight="bold" fill="black">展现交互层</text>
      
      <rect x="300" y="95" width="140" height="35" fill="#f0f0f0" stroke="black" stroke-width="1" rx="3"/>
      <text x="370" y="118" text-anchor="middle" font-size="12" fill="black">政府端门户</text>
      
      <rect x="460" y="95" width="140" height="35" fill="#f0f0f0" stroke="black" stroke-width="1" rx="3"/>
      <text x="530" y="118" text-anchor="middle" font-size="12" fill="black">企业端门户</text>
      
      <rect x="620" y="95" width="140" height="35" fill="#f0f0f0" stroke="black" stroke-width="1" rx="3"/>
      <text x="690" y="118" text-anchor="middle" font-size="12" fill="black">开放API</text>
      
      <rect x="780" y="95" width="160" height="35" fill="#f0f0f0" stroke="black" stroke-width="1" rx="3"/>
      <text x="860" y="118" text-anchor="middle" font-size="12" fill="black">移动应用</text>
      
      <!-- 业务应用层 -->
      <rect x="280" y="160" width="680" height="100" fill="white" stroke="black" stroke-width="2" rx="5"/>
      <text x="620" y="185" text-anchor="middle" font-size="15" font-weight="bold" fill="black">业务应用层</text>
      
      <rect x="300" y="200" width="100" height="45" fill="#e0e0e0" stroke="black" stroke-width="1" rx="3"/>
      <text x="350" y="218" text-anchor="middle" font-size="11" fill="black">备案管理</text>
      <text x="350" y="233" text-anchor="middle" font-size="11" fill="black">服务</text>
      
      <rect x="420" y="200" width="100" height="45" fill="#e0e0e0" stroke="black" stroke-width="1" rx="3"/>
      <text x="470" y="218" text-anchor="middle" font-size="11" fill="black">实时监控</text>
      <text x="470" y="233" text-anchor="middle" font-size="11" fill="black">服务</text>
      
      <rect x="540" y="200" width="100" height="45" fill="#e0e0e0" stroke="black" stroke-width="1" rx="3"/>
      <text x="590" y="218" text-anchor="middle" font-size="11" fill="black">风险预警</text>
      <text x="590" y="233" text-anchor="middle" font-size="11" fill="black">服务</text>
      
      <rect x="660" y="200" width="100" height="45" fill="#e0e0e0" stroke="black" stroke-width="1" rx="3"/>
      <text x="710" y="218" text-anchor="middle" font-size="11" fill="black">监督检查</text>
      <text x="710" y="233" text-anchor="middle" font-size="11" fill="black">服务</text>
      
      <rect x="780" y="200" width="100" height="45" fill="#e0e0e0" stroke="black" stroke-width="1" rx="3"/>
      <text x="830" y="218" text-anchor="middle" font-size="11" fill="black">溯源分析</text>
      <text x="830" y="233" text-anchor="middle" font-size="11" fill="black">服务</text>
      
      <!-- 平台服务层 -->
      <rect x="280" y="280" width="680" height="100" fill="white" stroke="black" stroke-width="2" rx="5"/>
      <text x="620" y="305" text-anchor="middle" font-size="15" font-weight="bold" fill="black">平台服务层</text>
      
      <rect x="300" y="320" width="120" height="45" fill="#d0d0d0" stroke="black" stroke-width="1" rx="3"/>
      <text x="360" y="338" text-anchor="middle" font-size="11" fill="black">大数据处理</text>
      <text x="360" y="353" text-anchor="middle" font-size="11" fill="black">Flink/Spark</text>
      
      <rect x="440" y="320" width="120" height="45" fill="#d0d0d0" stroke="black" stroke-width="1" rx="3"/>
      <text x="500" y="338" text-anchor="middle" font-size="11" fill="black">AI分析引擎</text>
      <text x="500" y="353" text-anchor="middle" font-size="11" fill="black">ML/DL模型</text>
      
      <rect x="580" y="320" width="120" height="45" fill="#d0d0d0" stroke="black" stroke-width="1" rx="3"/>
      <text x="640" y="338" text-anchor="middle" font-size="11" fill="black">GIS服务</text>
      <text x="640" y="353" text-anchor="middle" font-size="11" fill="black">空间分析</text>
      
      <rect x="720" y="320" width="120" height="45" fill="#d0d0d0" stroke="black" stroke-width="1" rx="3"/>
      <text x="780" y="338" text-anchor="middle" font-size="11" fill="black">区块链服务</text>
      <text x="780" y="353" text-anchor="middle" font-size="11" fill="black">存证验证</text>
      
      <rect x="860" y="320" width="80" height="45" fill="#d0d0d0" stroke="black" stroke-width="1" rx="3"/>
      <text x="900" y="338" text-anchor="middle" font-size="11" fill="black">消息队列</text>
      <text x="900" y="353" text-anchor="middle" font-size="11" fill="black">Kafka</text>
      
      <!-- 数据资源层 -->
      <rect x="280" y="400" width="680" height="100" fill="white" stroke="black" stroke-width="2" rx="5"/>
      <text x="620" y="425" text-anchor="middle" font-size="15" font-weight="bold" fill="black">数据资源层</text>
      
      <rect x="300" y="440" width="130" height="45" fill="#c0c0c0" stroke="black" stroke-width="1" rx="3"/>
      <text x="365" y="458" text-anchor="middle" font-size="11" fill="black">数据接入网关</text>
      <text x="365" y="473" text-anchor="middle" font-size="11" fill="black">多协议支持</text>
      
      <rect x="450" y="440" width="130" height="45" fill="#c0c0c0" stroke="black" stroke-width="1" rx="3"/>
      <text x="515" y="458" text-anchor="middle" font-size="11" fill="black">日志存储</text>
      <text x="515" y="473" text-anchor="middle" font-size="11" fill="black">ElasticSearch</text>
      
      <rect x="600" y="440" width="130" height="45" fill="#c0c0c0" stroke="black" stroke-width="1" rx="3"/>
      <text x="665" y="458" text-anchor="middle" font-size="11" fill="black">关系数据库</text>
      <text x="665" y="473" text-anchor="middle" font-size="11" fill="black">PostgreSQL</text>
      
      <rect x="750" y="440" width="130" height="45" fill="#c0c0c0" stroke="black" stroke-width="1" rx="3"/>
      <text x="815" y="458" text-anchor="middle" font-size="11" fill="black">时序数据库</text>
      <text x="815" y="473" text-anchor="middle" font-size="11" fill="black">InfluxDB</text>
      
      <!-- 基础设施层 -->
      <rect x="280" y="520" width="680" height="80" fill="white" stroke="black" stroke-width="2" rx="5"/>
      <text x="620" y="545" text-anchor="middle" font-size="15" font-weight="bold" fill="black">基础设施层</text>
      
      <rect x="300" y="555" width="180" height="35" fill="#b0b0b0" stroke="black" stroke-width="1" rx="3"/>
      <text x="390" y="578" text-anchor="middle" font-size="12" fill="black">计算资源(K8s集群)</text>
      
      <rect x="500" y="555" width="180" height="35" fill="#b0b0b0" stroke="black" stroke-width="1" rx="3"/>
      <text x="590" y="578" text-anchor="middle" font-size="12" fill="black">存储资源(日志优化存储)</text>
      
      <rect x="700" y="555" width="180" height="35" fill="#b0b0b0" stroke="black" stroke-width="1" rx="3"/>
      <text x="790" y="578" text-anchor="middle" font-size="12" fill="black">网络资源(SDN)</text>
    </g>
    
    <!-- 安全运维体系（右侧竖条） -->
    <g id="security-ops">
      <rect x="970" y="60" width="25" height="540" fill="#e0e0e0" stroke="black" stroke-width="2" rx="3"/>
      <text x="982" y="330" text-anchor="middle" font-size="14" font-weight="bold" fill="black" transform="rotate(90 982 330)">安全运维体系</text>
    </g>
    
    <!-- 数据流向说明文字 -->
    <text x="260" y="470" font-size="10" fill="black">处理日志</text>
    <text x="260" y="360" font-size="10" fill="black">汇总日志</text>
    <text x="260" y="250" font-size="10" fill="black">统计数据</text>
    <!-- 终端到企业 -->
    <polyline points="210,480 250,480 250,370 330,370" stroke="black" stroke-width="2" fill="none" marker-end="url(#arrow-right)"/>
    
    <!-- 企业到属地 -->
    <polyline points="210,370 250,370 250,260 330,260" stroke="black" stroke-width="2" fill="none" marker-end="url(#arrow-right)"/>
    
    <!-- 属地到国家 -->
    <polyline points="210,260 250,260 250,150 330,150" stroke="black" stroke-width="2" fill="none" marker-end="url(#arrow-right)"/>
    
    <!-- 层级之间的连接 -->
    <polyline points="620,140 620,150 620,160" stroke="black" stroke-width="1.5" fill="none" marker-end="url(#arrow-down)"/>
    <polyline points="620,260 620,270 620,280" stroke="black" stroke-width="1.5" fill="none" marker-end="url(#arrow-down)"/>
    <polyline points="620,380 620,390 620,400" stroke="black" stroke-width="1.5" fill="none" marker-end="url(#arrow-down)"/>
    <polyline points="620,500 620,510 620,520" stroke="black" stroke-width="1.5" fill="none" marker-end="url(#arrow-down)"/>
    
    <!-- 底部说明 -->
    <g id="legend">
      <rect x="30" y="620" width="940" height="150" fill="white" stroke="black" stroke-width="1" stroke-dasharray="5,5" rx="5"/>
      <text x="500" y="645" text-anchor="middle" font-size="14" font-weight="bold" fill="black">技术特点</text>
      
      <text x="50" y="670" font-size="12" fill="black">• 云原生架构：微服务、容器化、弹性伸缩</text>
      <text x="50" y="690" font-size="12" fill="black">• 高性能处理：支持百万级并发接入，海量日志实时处理</text>
      <text x="50" y="710" font-size="12" fill="black">• 智能化分析：规则引擎+AI模型双驱动</text>
      <text x="50" y="730" font-size="12" fill="black">• 安全可信：国密算法、区块链存证、多层防护</text>
      
      <text x="500" y="670" font-size="12" fill="black">• 标准化接口：遵循国家标准，支持多协议接入</text>
      <text x="500" y="690" font-size="12" fill="black">• 实时监控：秒级风险识别，分级预警响应</text>
      <text x="500" y="710" font-size="12" fill="black">• 全程可溯：数据血缘追踪，操作审计留痕</text>
      <text x="500" y="730" font-size="12" fill="black">• 开放共享：标准API接口，支持生态集成</text>
    </g>
    
    <!-- 箭头定义 -->
    <defs>
      <marker id="arrow-down" markerWidth="10" markerHeight="10" refX="5" refY="10" orient="auto">
        <polygon points="5 10, 10 0, 0 0" fill="black"/>
      </marker>
      <marker id="arrow-right" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
        <polygon points="10 5, 0 0, 0 10" fill="black"/>
      </marker>
    </defs>
    
  </svg>
  <svg width="900" height="900" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
    <!-- 背景 -->
    <!-- 标题 -->
    <!-- 需求牵引层 -->
    <!-- 标准规范层 -->
    <!-- 平台架构层 -->
    <!-- 技术支撑层 -->
    <!-- 应用服务层 -->
    <!-- 持续优化 -->
    <!-- 箭头定义 -->
    <defs>
     <marker id="arrowhead" markerHeight="7" markerWidth="10" orient="auto" refX="10" refY="3.5">
      <polygon fill="black" id="svg_1" points="0 0, 10 3.5, 0 7"/>
     </marker>
    </defs>
    <!-- 层级连接线 - 使用折线 -->
    <!-- 反馈循环 - 使用折线 -->
    <!-- 说明文字 -->
    <g class="layer">
     <title>Layer 1</title>
     <text fill="black" font-size="20" font-weight="bold" id="svg_4" text-anchor="middle" x="450" y="30">时空数据安全监测平台总体技术路线</text>
     <g id="demand-layer">
      <rect fill="white" height="80" id="svg_5" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="55"/>
      <text fill="black" font-size="16" font-weight="bold" id="svg_6" text-anchor="middle" x="450" y="85">需求牵引层</text>
      <!-- 需求模块 -->
      <rect fill="#f0f0f0" height="30" id="svg_7" rx="3" stroke="black" stroke-width="1.5" width="150" x="100" y="95"/>
      <text fill="black" font-size="14" id="svg_8" text-anchor="middle" x="175" y="115">政府安全监管需求</text>
      <rect fill="#f0f0f0" height="30" id="svg_9" rx="3" stroke="black" stroke-width="1.5" width="150" x="280" y="95"/>
      <text fill="black" font-size="14" id="svg_10" text-anchor="middle" x="355" y="115">行业应用需求</text>
      <rect fill="#f0f0f0" height="30" id="svg_11" rx="3" stroke="black" stroke-width="1.5" width="150" x="460" y="95"/>
      <text fill="black" font-size="14" id="svg_12" text-anchor="middle" x="535" y="115">企业合规需求</text>
      <rect fill="#f0f0f0" height="30" id="svg_13" rx="3" stroke="black" stroke-width="1.5" width="150" x="640" y="95"/>
      <text fill="black" font-size="14" id="svg_14" text-anchor="middle" x="715" y="115">数据安全需求</text>
     </g>
     <g id="standard-layer">
      <rect fill="white" height="80" id="svg_15" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="163"/>
      <text fill="black" font-size="16" font-weight="bold" id="svg_16" text-anchor="middle" x="450" y="193">标准规范体系</text>
      <rect fill="#e0e0e0" height="30" id="svg_17" rx="3" stroke="black" stroke-width="1.5" width="120" x="100" y="203"/>
      <text fill="black" font-size="13" id="svg_18" text-anchor="middle" x="160" y="223">数据分类分级</text>
      <rect fill="#e0e0e0" height="30" id="svg_19" rx="3" stroke="black" stroke-width="1.5" width="120" x="240" y="203"/>
      <text fill="black" font-size="13" id="svg_20" text-anchor="middle" x="300" y="223">安全处理规范</text>
      <rect fill="#e0e0e0" height="30" id="svg_21" rx="3" stroke="black" stroke-width="1.5" width="120" x="380" y="203"/>
      <text fill="black" font-size="13" id="svg_22" text-anchor="middle" x="440" y="223">通信协议标准</text>
      <rect fill="#e0e0e0" height="30" id="svg_23" rx="3" stroke="black" stroke-width="1.5" width="120" x="520" y="203"/>
      <text fill="black" font-size="13" id="svg_24" text-anchor="middle" x="580" y="223">监测预警规范</text>
      <rect fill="#e0e0e0" height="30" id="svg_25" rx="3" stroke="black" stroke-width="1.5" width="120" x="660" y="203"/>
      <text fill="black" font-size="13" id="svg_26" text-anchor="middle" x="720" y="223">接口规范标准</text>
     </g>
     <g id="platform-layer">
      <rect fill="white" height="120" id="svg_27" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="271"/>
      <text fill="black" font-size="16" font-weight="bold" id="svg_28" text-anchor="middle" x="450" y="301">四级监管架构体系</text>
      <!-- 四级架构 -->
      <rect fill="#d0d0d0" height="50" id="svg_29" rx="3" stroke="black" stroke-width="1.5" width="160" x="100" y="321"/>
      <text fill="black" font-size="14" id="svg_30" text-anchor="middle" x="180" y="351">国家监测平台</text>
      <rect fill="#d0d0d0" height="50" id="svg_31" rx="3" stroke="black" stroke-width="1.5" width="160" x="290" y="321"/>
      <text fill="black" font-size="14" id="svg_32" text-anchor="middle" x="370" y="351">属地监测平台</text>
      <rect fill="#d0d0d0" height="50" id="svg_33" rx="3" stroke="black" stroke-width="1.5" width="160" x="480" y="321"/>
      <text fill="black" font-size="14" id="svg_34" text-anchor="middle" x="560" y="351">企业平台</text>
      <rect fill="#d0d0d0" height="50" id="svg_35" rx="3" stroke="black" stroke-width="1.5" width="130" x="670" y="321"/>
      <text fill="black" font-size="14" id="svg_36" text-anchor="middle" x="735" y="351">终端节点</text>
      <!-- 连接线 - 使用折线 -->
      <polyline fill="none" id="svg_37" marker-end="url(#arrowhead)" points="260,346 275,346 275,346 290,346 " stroke="black" stroke-width="2"/>
      <polyline fill="none" id="svg_38" marker-end="url(#arrowhead)" points="450,346 465,346 465,346 480,346 " stroke="black" stroke-width="2"/>
      <polyline fill="none" id="svg_39" marker-end="url(#arrowhead)" points="640,346 655,346 655,346 670,346 " stroke="black" stroke-width="2"/>
     </g>
     <g id="tech-layer">
      <rect fill="white" height="120" id="svg_40" rx="5" stroke="black" stroke-width="2" width="800" x="50" y="416"/>
      <text fill="black" font-size="16" font-weight="bold" id="svg_41" text-anchor="middle" x="450" y="446">核心技术支撑</text>
      <!-- 技术模块 - 第一行 -->
      <rect fill="#f0f0f0" height="30" id="svg_42" rx="3" stroke="black" stroke-width="1.5" width="140" x="80" y="466"/>
      <text fill="black" font-size="12" id="svg_43" text-anchor="middle" x="150" y="486">风险识别与预警</text>
      <rect fill="#f0f0f0" height="30" id="svg_44" rx="3" stroke="black" stroke-width="1.5" width="140" x="240" y="466"/>
      <text fill="black" font-size="12" id="svg_45" text-anchor="middle" x="310" y="486">数据溯源追踪</text>
      <rect fill="#f0f0f0" height="30" id="svg_46" rx="3" stroke="black" stroke-width="1.5" width="140" x="400" y="466"/>
      <text fill="black" font-size="12" id="svg_47" text-anchor="middle" x="470" y="486">区块链存证</text>
      <rect fill="#f0f0f0" height="30" id="svg_48" rx="3" stroke="black" stroke-width="1.5" width="140" x="560" y="466"/>
      <text fill="black" font-size="12" id="svg_49" text-anchor="middle" x="630" y="486">实时流处理</text>
      <rect fill="#f0f0f0" height="30" id="svg_50" rx="3" stroke="black" stroke-width="1.5" width="110" x="720" y="466"/>
      <text fill="black" font-size="12" id="svg_51" text-anchor="middle" x="775" y="486">AI智能分析</text>
      <!-- 技术基础 -->
      <rect fill="#e0e0e0" height="20" id="svg_52" rx="3" stroke="black" width="500" x="200" y="506"/>
      <text fill="black" font-size="12" id="svg_53" text-anchor="middle" x="450" y="520">分布式架构 | 微服务 | 容器化 | PB级存储 | 国密算法</text>
     </g>
     <g id="service-layer">
      <rect fill="white" height="80" id="svg_54" rx="5" stroke="black" stroke-width="2" width="800" x="51" y="564"/>
      <text fill="black" font-size="16" font-weight="bold" id="svg_55" text-anchor="middle" x="451" y="594">应用服务创新</text>
      <rect fill="#d0d0d0" height="30" id="svg_56" rx="3" stroke="black" stroke-width="1.5" width="120" x="101" y="604"/>
      <text fill="black" font-size="13" id="svg_57" text-anchor="middle" x="161" y="624">安全预警</text>
      <rect fill="#d0d0d0" height="30" id="svg_58" rx="3" stroke="black" stroke-width="1.5" width="120" x="241" y="604"/>
      <text fill="black" font-size="13" id="svg_59" text-anchor="middle" x="301" y="624">监管决策</text>
      <rect fill="#d0d0d0" height="30" id="svg_60" rx="3" stroke="black" stroke-width="1.5" width="120" x="381" y="604"/>
      <text fill="black" font-size="13" id="svg_61" text-anchor="middle" x="441" y="624">合规评估</text>
      <rect fill="#d0d0d0" height="30" id="svg_62" rx="3" stroke="black" stroke-width="1.5" width="120" x="521" y="604"/>
      <text fill="black" font-size="13" id="svg_63" text-anchor="middle" x="581" y="624">应急响应</text>
      <rect fill="#d0d0d0" height="30" id="svg_64" rx="3" stroke="black" stroke-width="1.5" width="120" x="661" y="604"/>
      <text fill="black" font-size="13" id="svg_65" text-anchor="middle" x="721" y="624">数据共享</text>
     </g>
     <g id="optimize">
      <rect fill="white" height="40" id="svg_66" rx="5" stroke="black" stroke-dasharray="5,5" stroke-width="2" width="300" x="300" y="685"/>
      <text fill="black" font-size="14" id="svg_67" text-anchor="middle" x="450" y="710">持续优化迭代</text>
     </g>
     <polyline fill="none" id="svg_73" marker-end="url(#arrowhead)" points="299,710.0000095367432 21,710.0000095367432 21,405.0000047683716 21,100 46.272727966308594,100 " stroke="black" stroke-dasharray="3,3" stroke-width="1.5"/>
     <text fill="black" font-size="12" id="svg_74" transform="rotate(-90 34 371.25)" x="10" y="376">反馈优化</text>
     <g id="svg_78">
      <line fill="none" id="svg_76" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 446.544 657.665)" x1="436.348897" x2="456.738722" y1="657.665237" y2="657.665237"/>
      <path d="m441.326947,679.614333l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_77" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 446.544 674.129)"/>
     </g>
     <g id="svg_79">
      <line fill="none" id="svg_80" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 446.892 543.558)" x1="440.652101" x2="453.132323" y1="543.558149" y2="543.558149"/>
      <path d="m441.675322,561.552472l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_81" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 446.892 556.067)"/>
     </g>
     <g id="svg_82">
      <line fill="none" id="svg_83" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 447.458 398.36)" x1="441.217852" x2="453.698074" y1="398.360322" y2="398.360322"/>
      <path d="m442.241073,416.354645l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_84" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 447.458 410.869)"/>
     </g>
     <g id="svg_85">
      <line fill="none" id="svg_86" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 447.457 251.468)" x1="441.217319" x2="453.697541" y1="251.468072" y2="251.468072"/>
      <path d="m442.24054,269.462395l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_87" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 447.457 263.977)"/>
     </g>
     <g id="svg_88">
      <line fill="none" id="svg_89" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(90 447.458 142.993)" x1="441.218281" x2="453.698503" y1="142.992887" y2="142.992887"/>
      <path d="m442.241502,160.98721l5.216796,-10.970577l5.216796,10.970577l-10.433593,0z" fill="#000000" id="svg_90" stroke="black" stroke-dasharray="null" stroke-linecap="null" stroke-linejoin="null" stroke-width="2" transform="rotate(180 447.458 155.502)"/>
     </g>
    </g>
   </svg>








  <svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Created with SVG-edit - https://github.com/SVG-Edit/svgedit-->
    <defs>
     <symbol height="800" id="svg_89" width="1200" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
      <!-- 背景 -->
      <!-- 标题 -->
      <!-- 车端层 -->
      <!-- 企业端层 -->
      <!-- 政府端层（属地监测平台） -->
      <!-- 国家端层（预留） -->
      <!-- 车端到企业端通信 -->
      <!-- 企业端到政府端通信 -->
      <!-- 政府端到国家端通信（预留） -->
      <!-- 数据分类图例 -->
      <!-- 通信机制说明 -->
      <!-- 箭头定义 -->
      <defs>
       <marker id="svg_55" markerHeight="10" markerWidth="10" orient="auto" refX="8" refY="3">
        <polygon fill="black" id="svg_88" points="0 0, 10 3, 0 6"/>
       </marker>
       <marker id="svg_53" markerHeight="10" markerWidth="10" orient="auto" refX="8" refY="3">
        <polygon fill="black" id="svg_87" points="0 0, 10 3, 0 6"/>
       </marker>
       <marker id="svg_50" markerHeight="10" markerWidth="10" orient="auto" refX="8" refY="3">
        <polygon fill="gray" id="svg_86" points="0 0, 10 3, 0 6"/>
       </marker>
      </defs>
      <g class="layer">
       <title>Layer 1</title>
       <text fill="black" font-size="24" font-weight="bold" id="svg_85" text-anchor="middle" x="598" y="67">时空数据安全监测平台 总体通信架构图</text>
       <g id="svg_79">
        <rect fill="white" height="135.999999" id="svg_84" stroke="black" stroke-width="2" width="391.000009" x="713" y="440"/>
        <text fill="black" font-size="16" font-weight="bold" id="svg_83" text-anchor="middle" x="912" y="482">车端</text>
        <text font-size="12" id="svg_82" text-anchor="middle" x="912" y="507">（车载终端）</text>
        <text font-size="12" id="svg_81" text-anchor="middle" x="912" y="532">• 数据采集设备</text>
        <text font-size="12" id="svg_80" text-anchor="middle" x="912" y="552">• 监管插件</text>
       </g>
       <g id="svg_72">
        <rect fill="white" height="140" id="svg_78" stroke="black" stroke-width="2" width="397.000031" x="100" y="437"/>
        <text font-size="16" font-weight="bold" id="svg_77" text-anchor="middle" x="300" y="467">企业端</text>
        <text font-size="12" id="svg_76" text-anchor="middle" x="300" y="492">（企业数据中心）</text>
        <text font-size="12" id="svg_75" text-anchor="middle" x="300" y="517">• 数据汇聚处理</text>
        <text fill="black" font-size="12" id="svg_74" text-anchor="middle" x="300" y="537">• 监管日志生成</text>
        <text font-size="12" id="svg_73" text-anchor="middle" x="300" y="557">• 统计分析</text>
       </g>
       <g id="svg_65">
        <rect fill="white" height="136" id="svg_71" stroke="black" stroke-width="2" width="400" x="100" y="120"/>
        <text fill="black" font-size="16" font-weight="bold" id="svg_70" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="302" y="147.82355">政府端</text>
        <text fill="black" font-size="12" id="svg_69" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="173.82355">（属地监测平台）</text>
        <text font-size="12" id="svg_68" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="198.82355">• 实时监控</text>
        <text font-size="12" id="svg_67" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="218.82355">• 风险预警</text>
        <text font-size="12" id="svg_66" text-anchor="middle" transform="matrix(1 0 0 0.971429 0 4.57143)" x="300" y="238.82355">• 合规审查</text>
       </g>
       <g id="svg_58">
        <rect fill="white" height="140" id="svg_64" stroke="black" stroke-dasharray="5,5" stroke-width="2" width="400" x="710" y="117"/>
        <text fill="gray" font-size="16" font-weight="bold" id="svg_63" text-anchor="middle" x="910" y="147">国家端（预留）</text>
        <text fill="gray" font-size="12" id="svg_62" text-anchor="middle" x="910" y="172">（国家监测平台）</text>
        <text fill="gray" font-size="12" id="svg_61" text-anchor="middle" x="910" y="197">• 宏观监管</text>
        <text fill="gray" font-size="12" id="svg_60" text-anchor="middle" x="910" y="217">• 政策制定</text>
        <text fill="gray" font-size="12" id="svg_59" text-anchor="middle" x="910" y="237">• 跨域协调</text>
       </g>
       <g id="svg_56">
        <!-- 上行箭头 -->
        <path d="m604,604l0,-61.285717l0,-65.000002l0,-81.714289" fill="none" id="svg_57" marker-end="url(#svg_55)" stroke="black" stroke-width="2" transform="rotate(-90 604 500)"/>
        <!-- 上行数据说明 -->
       </g>
       <g id="svg_51">
        <!-- 上行箭头 -->
        <path d="m340,432.999997l0,-86.500001l0,-86.500001" fill="none" id="svg_54" marker-end="url(#svg_55)" stroke="black" stroke-width="2"/>
        <!-- 上行数据说明 -->
        <!-- 下行箭头 -->
        <path d="m250,257.999995l0,86.000003l0,86.000003" fill="none" id="svg_52" marker-end="url(#svg_53)" stroke="black" stroke-dasharray="3,3" stroke-width="2"/>
        <!-- 下行数据说明 -->
       </g>
       <g id="svg_48">
        <!-- 横向连接线 -->
        <path d="m500.999997,187l102.999997,0l0,0l102.999997,0" fill="none" id="svg_49" marker-end="url(#svg_50)" stroke="gray" stroke-dasharray="5,5" stroke-width="2"/>
        <!-- 数据说明 -->
       </g>
       <g id="svg_37">
        <rect fill="white" height="131.000012" id="svg_47" stroke="black" width="349.999995" x="52" y="643"/>
        <text fill="black" font-size="14" font-weight="bold" id="svg_46" text-anchor="middle" transform="matrix(1.01786 0 0 1 -3.94644 0)" x="222.4736" y="663">[数据分类]</text>
        <text fill="black" font-size="12" font-weight="bold" id="svg_45" x="72" y="688">1. 连接管理类</text>
        <text fill="black" font-size="11" id="svg_44" x="82" y="708">登入/登出、心跳、鉴权</text>
        <text fill="black" font-size="12" font-weight="bold" id="svg_43" x="71" y="733">2. 处理流程数据</text>
        <text fill="black" font-size="11" id="svg_42" x="82" y="753">8个生命周期环节数据</text>
        <text fill="black" font-size="12" font-weight="bold" id="svg_41" x="261" y="688">3. 事件数据</text>
        <text font-size="11" id="svg_40" x="271" y="708">异常事件实时上报</text>
        <text font-size="12" font-weight="bold" id="svg_39" x="261" y="733">4. 统计数据</text>
        <text font-size="11" id="svg_38" x="271" y="753">定期统计汇总信息</text>
       </g>
       <g id="svg_30">
        <rect fill="white" height="131.00001" id="svg_36" stroke="black" width="300" x="415" y="643"/>
        <text fill="black" font-size="14" font-weight="bold" id="svg_35" text-anchor="middle" x="565" y="666">[通信机制]</text>
        <text font-size="11" id="svg_34" x="435" y="691">• TCP/IP长连接</text>
        <text font-size="11" id="svg_33" x="435" y="711">• 心跳保活（默认60秒）</text>
        <text font-size="11" id="svg_32" x="435" y="731">• 数据加密传输</text>
        <text font-size="11" id="svg_31" x="435" y="751">• 断线重连与补发机制</text>
       </g>
       <g id="svg_25">
        <rect fill="white" height="60" id="svg_29" stroke="gray" stroke-dasharray="3,3" width="160" x="524" y="102"/>
        <text fill="gray" font-size="12" font-weight="bold" id="svg_28" x="534" y="122">预留接口</text>
        <text fill="gray" font-size="11" id="svg_27" x="534" y="137">• 统计汇总数据</text>
        <text fill="gray" font-size="11" id="svg_26" x="534" y="152">• 重大事件上报</text>
       </g>
       <g id="svg_18">
        <rect fill="white" height="100" id="svg_24" stroke="black" width="153.999996" x="528" y="515"/>
        <text font-size="12" font-weight="bold" id="svg_23" x="538" y="535">上行数据（0x01-0x05）</text>
        <text font-size="11" id="svg_22" x="538" y="555">• 车辆登入/登出</text>
        <text fill="black" font-size="11" id="svg_21" x="538" y="570">• 处理流程数据</text>
        <text font-size="11" id="svg_20" x="538" y="585">• 事件数据上报</text>
        <text font-size="11" id="svg_19" x="538" y="600">• 补发数据上报</text>
       </g>
       <g id="svg_9">
        <rect fill="white" height="120" id="svg_17" stroke="black" width="200" x="359" y="286"/>
        <text font-size="12" font-weight="bold" id="svg_16" x="369" y="307">上行数据（0x40-0x48）</text>
        <text font-size="11" id="svg_15" x="369" y="322">• 企业鉴权/心跳</text>
        <text fill="black" font-size="11" id="svg_14" x="369" y="337">• 处理流程数据汇总</text>
        <text fill="black" font-size="11" id="svg_13" x="369" y="352">• 事件数据汇总</text>
        <text font-size="11" id="svg_12" x="369" y="367">• 统计数据上报</text>
        <text font-size="11" id="svg_11" x="369" y="382">• 数据查询响应</text>
        <text font-size="11" id="svg_10" x="369" y="397">• 补发数据上报</text>
       </g>
       <g id="svg_1">
        <rect fill="white" height="105" id="svg_8" stroke="black" width="180" x="54" y="291.5"/>
        <text font-size="12" font-weight="bold" id="svg_7" x="64" y="311.5">下行数据</text>
        <text font-size="11" id="svg_6" x="64" y="326.5">• 平台通用应答(0x41)</text>
        <text font-size="11" id="svg_5" x="64" y="341.5">• 鉴权应答(0x44)</text>
        <text font-size="11" id="svg_4" x="64" y="356.5">• 数据查询(0x49)</text>
        <text font-size="11" id="svg_3" x="64" y="371.5">• 传输参数控制(0x80)</text>
        <text font-size="11" id="svg_2" x="64" y="386.5">• 密钥交换(0x4B)</text>
       </g>
      </g>
     </symbol>
    </defs>
    <g class="layer">
     <title>Layer 1</title>
     <use id="svg_90" transform="matrix(1.0404 0 0 1.0404 -38.8636 -30.8864)" x="0" xlink:href="#svg_89" y="0"/>
     <g id="svg_91"/>
    </g>
   </svg>


   <svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
    <!-- 标题 -->
    <!-- 定义箭头标记 -->
    <defs>
     <marker id="arrowhead" markerHeight="7" markerWidth="10" orient="auto" refX="9" refY="3.5">
      <polygon fill="black" id="svg_1" points="0 0, 10 3.5, 0 7"/>
     </marker>
    </defs>
    <!-- 步骤1: 连接管理 -->
    <!-- 步骤2: 连接维持 -->
    <!-- 步骤3: 数据采集 -->
    <!-- 步骤4: 数据校验 -->
    <!-- 步骤5: 风险检测 -->
    <!-- 步骤6: 数据上报 -->
    <!-- 步骤7: 数据存储 -->
    <!-- 步骤8: 数据审计 -->
    <!-- 连接线 步骤1 → 步骤2 -->
    <!-- 连接线 步骤2 → 步骤3 -->
    <!-- 连接线 步骤3 → 步骤4 (折线，向下) -->
    <!-- 连接线 步骤4 → 步骤5 (折线，向左) -->
    <!-- 连接线 步骤5 → 步骤6 (折线，向左) -->
    <!-- 连接线 步骤6 → 步骤7 (折线，向下) -->
    <!-- 连接线 步骤7 → 步骤8 (折线，向右) -->
    <!-- 循环反馈线：步骤8 → 步骤3 (长折线) -->
    <!-- 循环反馈标签 -->
    <!-- 流程说明框 -->
    <!-- 技术特点框 -->
    <g class="layer">
     <title>Layer 1</title>
     <text fill="black" font-family="Arial, sans-serif" font-size="20" font-weight="bold" id="svg_2" text-anchor="middle" x="501" y="41">数据处理流程图</text>
     <g id="step1"/>
     <g id="step2"/>
     <g id="step3"/>
     <g id="step4"/>
     <g id="step5"/>
     <g id="step6"/>
     <g id="step7"/>
     <g id="step8"/>
     <g id="legend">
      <rect fill="white" height="220" id="svg_44" rx="5" stroke="black" width="400" x="80" y="500"/>
      <text fill="black" font-size="16" font-weight="bold" id="svg_45" text-anchor="middle" x="280" y="525">流程说明</text>
      <text fill="black" font-size="12" id="svg_46" x="100" y="550">• 企业数据中心与属地监测平台建立安全连接</text>
      <text fill="black" font-size="12" id="svg_47" x="100" y="570">• 通过心跳机制维持连接稳定性</text>
      <text fill="black" font-size="12" id="svg_48" x="100" y="590">• 实时采集车端和云端数据处理流程信息</text>
      <text fill="black" font-size="12" id="svg_49" x="100" y="610">• 多层次数据校验确保数据质量</text>
      <text fill="black" font-size="12" id="svg_50" x="100" y="630">• 基于风险清单进行智能风险识别</text>
      <text fill="black" font-size="12" id="svg_51" x="100" y="650">• 按协议规范批量上报各类监测数据</text>
      <text fill="black" font-size="12" id="svg_52" x="100" y="670">• 分类存储支持后续查询分析</text>
      <text fill="black" font-size="12" id="svg_53" x="100" y="690">• 定期审计保障合规性</text>
      <text fill="black" font-size="12" font-style="italic" id="svg_54" x="100" y="710">虚线表示持续监测的循环反馈机制</text>
     </g>
     <g id="features">
      <rect fill="white" height="220" id="svg_55" rx="5" stroke="black" width="400" x="520" y="500"/>
      <text fill="black" font-size="16" font-weight="bold" id="svg_56" text-anchor="middle" x="720" y="525">技术特点</text>
      <text fill="black" font-size="12" id="svg_57" x="540" y="550">✓ 实时性：秒级数据采集和风险检测</text>
      <text fill="black" font-size="12" id="svg_58" x="540" y="570">✓ 安全性：国密算法加密和数字签名</text>
      <text fill="black" font-size="12" id="svg_59" x="540" y="590">✓ 可靠性：断线重连和数据补发机制</text>
      <text fill="black" font-size="12" id="svg_60" x="540" y="610">✓ 智能性：基于规则引擎和AI的风险识别和预警</text>
      <text fill="black" font-size="12" id="svg_61" x="540" y="630">✓ 标准化：统一协议和数据格式规范</text>
      <text fill="black" font-size="12" id="svg_62" x="540" y="650">✓ 扩展性：支持多业务场景和新需求</text>
      <text fill="black" font-size="12" id="svg_63" x="540" y="670">✓ 审计性：完整的操作日志和溯源能力</text>
      <text fill="black" font-size="12" id="svg_64" x="540" y="690">✓ 合规性：符合国家数据安全法规要求</text>
     </g>
     <g id="svg_65">
      <rect fill="white" height="80" id="svg_3" rx="5" stroke="black" stroke-width="2" width="180" x="171" y="77"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_4" text-anchor="middle" x="261" y="102">1. 连接管理</text>
      <text fill="black" font-size="11" id="svg_5" text-anchor="middle" x="261" y="122">企业数据中心连接到</text>
      <text fill="black" font-size="11" id="svg_6" text-anchor="middle" x="261" y="137">属地监测平台并进行鉴权</text>
      <rect fill="white" height="80" id="svg_7" rx="5" stroke="black" stroke-width="2" width="180" x="411" y="77"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_8" text-anchor="middle" x="501" y="102">2. 连接维持</text>
      <text fill="black" font-size="11" id="svg_9" text-anchor="middle" x="501" y="122">通过心跳机制</text>
      <text fill="black" font-size="11" id="svg_10" text-anchor="middle" x="501" y="137">保持连接活跃</text>
      <rect fill="white" height="80" id="svg_11" rx="5" stroke="black" stroke-width="2" width="180" x="651" y="77"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_12" text-anchor="middle" x="741" y="102">3. 数据采集</text>
      <text fill="black" font-size="11" id="svg_13" text-anchor="middle" x="741" y="122">按照规定采集时空数据</text>
      <text fill="black" font-size="11" id="svg_14" text-anchor="middle" x="741" y="137">处理流程信息</text>
      <rect fill="white" height="80" id="svg_15" rx="5" stroke="black" stroke-width="2" width="180" x="651" y="217"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_16" text-anchor="middle" x="741" y="242">4. 数据校验</text>
      <text fill="black" font-size="11" id="svg_17" text-anchor="middle" x="741" y="262">验证数据的有效性</text>
      <text fill="black" font-size="11" id="svg_18" text-anchor="middle" x="741" y="277">和完整性，核验数据质量</text>
      <rect fill="white" height="80" id="svg_19" rx="5" stroke="black" stroke-width="2" width="180" x="411" y="217"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_20" text-anchor="middle" x="501" y="242">5. 风险检测</text>
      <text fill="black" font-size="11" id="svg_21" text-anchor="middle" x="501" y="262">检测是否存在</text>
      <text fill="black" font-size="11" id="svg_22" text-anchor="middle" x="501" y="277">风险事件</text>
      <rect fill="white" height="80" id="svg_23" rx="5" stroke="black" stroke-width="2" width="180" x="171" y="217"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_24" text-anchor="middle" x="261" y="242">6. 数据上报</text>
      <text fill="black" font-size="11" id="svg_25" text-anchor="middle" x="261" y="262">按照协议规定上报处理</text>
      <text fill="black" font-size="11" id="svg_26" text-anchor="middle" x="261" y="277">流程、事件和统计数据</text>
      <rect fill="white" height="80" id="svg_27" rx="5" stroke="black" stroke-width="2" width="180" x="171" y="357"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_28" text-anchor="middle" x="261" y="382">7. 数据存储</text>
      <text fill="black" font-size="11" id="svg_29" text-anchor="middle" x="261" y="402">保存数据以备</text>
      <text fill="black" font-size="11" id="svg_30" text-anchor="middle" x="261" y="417">后续查询和统计</text>
      <rect fill="white" height="80" id="svg_31" rx="5" stroke="black" stroke-width="2" width="180" x="411" y="357"/>
      <text fill="black" font-size="14" font-weight="bold" id="svg_32" text-anchor="middle" x="501" y="382">8. 数据审计</text>
      <text fill="black" font-size="11" id="svg_33" text-anchor="middle" x="501" y="402">定期审计</text>
      <text fill="black" font-size="11" id="svg_34" text-anchor="middle" x="501" y="417">数据处理活动</text>
      <path d="m351,117l30,0l0,0l30,0" fill="none" id="svg_35" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
      <path d="m591,117l30,0l0,0l30,0" fill="none" id="svg_36" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
      <path d="m741,157l0,30l0,0l0,30" fill="none" id="svg_37" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
      <path d="m651,257l-30,0l0,0l-30,0" fill="none" id="svg_38" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
      <path d="m411,257l-30,0l0,0l-30,0" fill="none" id="svg_39" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
      <path d="m261,297l0,30l0,0l0,30" fill="none" id="svg_40" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
      <path d="m351,397l30,0l0,0l30,0" fill="none" id="svg_41" marker-end="url(#arrowhead)" stroke="black" stroke-width="2"/>
      <path d="m501,357l0,-30l370,0l0,-210l-30,0l-10,0" fill="none" id="svg_42" marker-end="url(#arrowhead)" stroke="black" stroke-dasharray="5,5" stroke-width="2"/>
      <text fill="black" font-size="10" id="svg_43" text-anchor="middle" x="671" y="322">持续监测循环</text>
     </g>
    </g>
   </svg>



  <h3>三级架构通信模型</h3>
  <svg width="800" height="480" xmlns="http://www.w3.org/2000/svg">
    <!-- 背景和边框 -->
    <rect width="800" height="480" fill="white" stroke="none"/>
    
    <!-- 标题 -->
    <text x="400" y="30" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">智能网联汽车时空数据安全监测平台通信架构</text>
    
    <!-- 国家监管平台 -->
    <rect x="580" y="60" width="200" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="680" y="85" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">国家监管平台</text>
    <rect x="590" y="95" width="80" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="630" y="110" font-family="Arial" font-size="10" text-anchor="middle">车辆数据库</text>
    <rect x="590" y="125" width="80" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="630" y="140" font-family="Arial" font-size="10" text-anchor="middle">数据安全分析</text>
    <rect x="680" y="95" width="80" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="720" y="110" font-family="Arial" font-size="10" text-anchor="middle">统计信息</text>
    <rect x="680" y="125" width="80" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="720" y="140" font-family="Arial" font-size="10" text-anchor="middle">企业信息</text>
    
    <!-- 数据查询/预警信息下发 -->
    <line x1="680" y1="170" x2="680" y2="190" stroke="black" stroke-width="1.5" stroke-dasharray="5,3"/>
    <polyline points="670,180 680,190 690,180" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="765" y="180" font-family="Arial" font-size="10" text-anchor="middle">数据查询/预警信息下发</text>
    
    <!-- 属地监管平台 -->
    <rect x="320" y="200" width="440" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="540" y="225" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">属地监管平台</text>
    <rect x="330" y="240" width="95" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="377" y="255" font-family="Arial" font-size="10" text-anchor="middle">车辆地理数据库</text>
    <rect x="435" y="240" width="95" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="482" y="255" font-family="Arial" font-size="10" text-anchor="middle">数据安全风险分析</text>
    <rect x="540" y="240" width="95" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="587" y="255" font-family="Arial" font-size="10" text-anchor="middle">统计信息汇总</text>
    <rect x="645" y="240" width="95" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="692" y="255" font-family="Arial" font-size="10" text-anchor="middle">企业信息管理</text>
    
    <!-- 统计信息周期上报 -->
    <line x1="680" y1="160" x2="680" y2="190" stroke="black" stroke-width="1.5"/>
    <polyline points="675,170 680,160 685,170" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="595" y="175" font-family="Arial" font-size="10" text-anchor="middle">统计信息周期上报</text>
    
    <!-- 安全风险/事件处理实时上报 -->
    <line x1="540" y1="160" x2="540" y2="190" stroke="black" stroke-width="1.5"/>
    <polyline points="535,170 540,160 545,170" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="455" y="175" font-family="Arial" font-size="10" text-anchor="middle">安全风险/事件处理实时上报</text>
    
    <!-- 数据查询/预警信息下发 -->
    <line x1="540" y1="310" x2="540" y2="330" stroke="black" stroke-width="1.5" stroke-dasharray="5,3"/>
    <polyline points="530,320 540,330 550,320" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="620" y="320" font-family="Arial" font-size="10" text-anchor="middle">数据查询/预警信息下发</text>
    
    <!-- 企业平台 -->
    <rect x="120" y="340" width="320" height="100" fill="white" stroke="black" stroke-width="2"/>
    <text x="280" y="365" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">企业平台</text>
    <rect x="130" y="380" width="70" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="165" y="395" font-family="Arial" font-size="10" text-anchor="middle">车辆基本信息</text>
    <rect x="210" y="380" width="70" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="245" y="395" font-family="Arial" font-size="10" text-anchor="middle">安全风险数据</text>
    <rect x="290" y="380" width="70" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="325" y="395" font-family="Arial" font-size="10" text-anchor="middle">统计信息</text>
    <rect x="370" y="380" width="60" height="25" fill="white" stroke="black" stroke-width="1"/>
    <text x="400" y="395" font-family="Arial" font-size="10" text-anchor="middle">企业备案</text>
    
    <!-- 车辆端数据上报 -->
    <line x1="165" y1="300" x2="165" y2="330" stroke="black" stroke-width="1.5"/>
    <polyline points="160,310 165,300 170,310" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="165" y="290" font-family="Arial" font-size="10" text-anchor="middle">车辆时空数据上报（预留）</text>
    
    <!-- 安全风险/事件实时上报 -->
    <line x1="280" y1="300" x2="280" y2="330" stroke="black" stroke-width="1.5"/>
    <polyline points="275,310 280,300 285,310" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="280" y="290" font-family="Arial" font-size="10" text-anchor="middle">安全风险/事件实时上报</text>
    
    <!-- 车辆数据统计上报 -->
    <line x1="430" y1="300" x2="430" y2="330" stroke="black" stroke-width="1.5"/>
    <polyline points="425,310 430,300 435,310" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="430" y="290" font-family="Arial" font-size="10" text-anchor="middle">车辆数据统计上报</text>
    
    <!-- 车辆端数据上报(企业) -->
    <line x1="540" y1="300" x2="280" y2="330" stroke="black" stroke-width="1.5"/>
    <polyline points="290,323 280,330 274,318" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="320" font-family="Arial" font-size="10" text-anchor="middle">车辆数据上报</text>
    
    <!-- 数据查询/预警信息下发 -->
    <line x1="300" y1="450" x2="300" y2="470" stroke="black" stroke-width="1.5" stroke-dasharray="5,3"/>
    <polyline points="290,460 300,470 310,460" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="375" y="460" font-family="Arial" font-size="10" text-anchor="middle">数据查询/预警信息下发</text>
    
    <!-- 车载终端 -->
    <rect x="100" y="130" width="130" height="30" fill="white" stroke="black" stroke-width="2"/>
    <text x="165" y="150" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">车载终端</text>
    
    <!-- 车载终端数据上报 -->
    <line x1="165" y1="160" x2="165" y2="190" stroke="black" stroke-width="1.5"/>
    <polyline points="160,170 165,160 170,170" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="165" y="205" font-family="Arial" font-size="10" text-anchor="middle">车端时空数据上报</text>
  </svg>

  <h3>二进制协议数据包结构</h3>
  <svg width="800" height="320" xmlns="http://www.w3.org/2000/svg">
    <!-- 背景和边框 -->
    <rect width="800" height="320" fill="white" stroke="none"/>
    
    <!-- 数据包标题 -->
    <text x="400" y="30" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">时空数据安全监测平台二进制数据包结构</text>
    
    <!-- 数据包结构 -->
    <rect x="50" y="60" width="700" height="60" fill="white" stroke="black" stroke-width="2"/>
    
    <!-- 数据包字段 -->
    <line x1="100" y1="60" x2="100" y2="120" stroke="black" stroke-width="1"/>
    <line x1="170" y1="60" x2="170" y2="120" stroke="black" stroke-width="1"/>
    <line x1="240" y1="60" x2="240" y2="120" stroke="black" stroke-width="1"/>
    <line x1="310" y1="60" x2="310" y2="120" stroke="black" stroke-width="1"/>
    <line x1="380" y1="60" x2="380" y2="120" stroke="black" stroke-width="1"/>
    <line x1="450" y1="60" x2="450" y2="120" stroke="black" stroke-width="1"/>
    <line x1="520" y1="60" x2="520" y2="120" stroke="black" stroke-width="1"/>
    <line x1="680" y1="60" x2="680" y2="120" stroke="black" stroke-width="1"/>
    
    <!-- 字段名称 -->
    <text x="75" y="90" font-family="Arial" font-size="12" text-anchor="middle">起始符</text>
    <text x="75" y="105" font-family="Arial" font-size="10" text-anchor="middle">(2B)</text>
    
    <text x="135" y="90" font-family="Arial" font-size="12" text-anchor="middle">命令单元</text>
    <text x="135" y="105" font-family="Arial" font-size="10" text-anchor="middle">(2B)</text>
    
    <text x="205" y="90" font-family="Arial" font-size="12" text-anchor="middle">识别码</text>
    <text x="205" y="105" font-family="Arial" font-size="10" text-anchor="middle">(16B)</text>
    
    <text x="275" y="90" font-family="Arial" font-size="12" text-anchor="middle">时间戳</text>
    <text x="275" y="105" font-family="Arial" font-size="10" text-anchor="middle">(13B)</text>
    
    <text x="345" y="90" font-family="Arial" font-size="12" text-anchor="middle">消息ID</text>
    <text x="345" y="105" font-family="Arial" font-size="10" text-anchor="middle">(9B)</text>
    
    <text x="415" y="90" font-family="Arial" font-size="12" text-anchor="middle">加密方式</text>
    <text x="415" y="105" font-family="Arial" font-size="10" text-anchor="middle">(1B)</text>
    
    <text x="485" y="90" font-family="Arial" font-size="12" text-anchor="middle">数据长度</text>
    <text x="485" y="105" font-family="Arial" font-size="10" text-anchor="middle">(2B)</text>
    
    <text x="600" y="90" font-family="Arial" font-size="12" text-anchor="middle">数据单元</text>
    <text x="600" y="105" font-family="Arial" font-size="10" text-anchor="middle">(变长)</text>
    
    <text x="715" y="90" font-family="Arial" font-size="12" text-anchor="middle">校验码</text>
    <text x="715" y="105" font-family="Arial" font-size="10" text-anchor="middle">(1B)</text>
    
    <!-- 命令单元拆分 -->
    <rect x="50" y="150" width="240" height="60" fill="white" stroke="black" stroke-width="2"/>
    <line x1="170" y1="150" x2="170" y2="210" stroke="black" stroke-width="1"/>
    <text x="110" y="180" font-family="Arial" font-size="12" text-anchor="middle">命令标识</text>
    <text x="110" y="195" font-family="Arial" font-size="10" text-anchor="middle">(1B)</text>
    <text x="220" y="180" font-family="Arial" font-size="12" text-anchor="middle">应答标志</text>
    <text x="220" y="195" font-family="Arial" font-size="10" text-anchor="middle">(1B)</text>
    
    <!-- 连接线 -->
    <line x1="135" y1="120" x2="135" y2="135" stroke="black" stroke-width="1"/>
    <line x1="135" y1="135" x2="170" y2="150" stroke="black" stroke-width="1"/>
    
    <!-- 校验过程 -->
    <rect x="400" y="150" width="350" height="60" fill="white" stroke="black" stroke-width="2"/>
    <text x="575" y="180" font-family="Arial" font-size="12" text-anchor="middle">BCC异或校验范围(从命令单元至数据单元末尾)</text>
    <text x="575" y="195" font-family="Arial" font-size="10" text-anchor="middle">校验结果存储在校验码字段</text>
    
    <!-- 连接线 -->
    <line x1="735" y1="120" x2="735" y2="135" stroke="black" stroke-width="1"/>
    <line x1="735" y1="135" x2="575" y2="150" stroke="black" stroke-width="1"/>
    
    <!-- 命令类型说明 -->
    <rect x="50" y="240" width="700" height="70" fill="white" stroke="black" stroke-width="2"/>
    <text x="400" y="260" font-family="Arial" font-size="12" text-anchor="middle">命令标识范围: 0x01-0x3F (车端命令), 0x40-0x7F (企业平台命令)</text>
    <text x="400" y="280" font-family="Arial" font-size="12" text-anchor="middle">主要命令: 登入/登出(0x01/0x02), 处理流程数据上报(0x03/0x45), 事件数据上报(0x04/0x46)</text>
    <text x="400" y="300" font-family="Arial" font-size="12" text-anchor="middle">应答标志: 0x01(成功), 0x02(错误), 0x03(ID重复), 0x04(ID不存在), 0x05(鉴权错误), 0xFE(命令)</text>
  </svg>

  <h3>通信流程</h3>
  <svg width="800" height="950" xmlns="http://www.w3.org/2000/svg">
    <!-- 背景和边框 -->
    <rect width="800" height="950" fill="white" stroke="none"/>
    
    <!-- 标题 -->
    <text x="400" y="30" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">通信流程图</text>
    
    <!-- 客户端平台 -->
    <rect x="100" y="60" width="200" height="40" fill="white" stroke="black" stroke-width="2"/>
    <text x="200" y="85" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">客户端平台</text>
    <line x1="200" y1="100" x2="200" y2="930" stroke="black" stroke-width="1" stroke-dasharray="5,5"/>
    
    <!-- 服务端平台 -->
    <rect x="500" y="60" width="200" height="40" fill="white" stroke="black" stroke-width="2"/>
    <text x="600" y="85" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">服务端平台</text>
    <line x1="600" y1="100" x2="600" y2="930" stroke="black" stroke-width="1" stroke-dasharray="5,5"/>
    
    <!-- 连接建立 -->
    <line x1="200" y1="140" x2="600" y2="160" stroke="black" stroke-width="1.5"/>
    <polyline points="590,155 600,160 590,165" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="140" font-family="Arial" font-size="12" text-anchor="middle">1. TCP连接请求</text>
    
    <line x1="600" y1="180" x2="200" y2="200" stroke="black" stroke-width="1.5"/>
    <polyline points="210,195 200,200 210,205" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="180" font-family="Arial" font-size="12" text-anchor="middle">2. TCP连接建立</text>
    
    <!-- 鉴权 -->
    <line x1="200" y1="220" x2="600" y2="240" stroke="black" stroke-width="1.5"/>
    <polyline points="590,235 600,240 590,245" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="220" font-family="Arial" font-size="12" text-anchor="middle">3. 企业鉴权(0x43)</text>
    
    <line x1="600" y1="260" x2="200" y2="280" stroke="black" stroke-width="1.5"/>
    <polyline points="210,275 200,280 210,285" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="260" font-family="Arial" font-size="12" text-anchor="middle">4. 企业鉴权应答(0x44)</text>
    
    <!-- 数据上报 -->
    <line x1="200" y1="320" x2="600" y2="340" stroke="black" stroke-width="1.5"/>
    <polyline points="590,335 600,340 590,345" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="320" font-family="Arial" font-size="12" text-anchor="middle">5. 处理流程数据上报(0x45)</text>
    
    <line x1="600" y1="360" x2="200" y2="380" stroke="black" stroke-width="1.5"/>
    <polyline points="210,375 200,380 210,385" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="360" font-family="Arial" font-size="12" text-anchor="middle">6. 平台通用应答(0x41)</text>
    
    <line x1="200" y1="420" x2="600" y2="440" stroke="black" stroke-width="1.5"/>
    <polyline points="590,435 600,440 590,445" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="420" font-family="Arial" font-size="12" text-anchor="middle">7. 事件数据上报(0x46)</text>
    
    <line x1="600" y1="460" x2="200" y2="480" stroke="black" stroke-width="1.5"/>
    <polyline points="210,475 200,480 210,485" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="460" font-family="Arial" font-size="12" text-anchor="middle">8. 平台通用应答(0x41)</text>
    
    <!-- 数据补发流程 -->
    <rect x="50" y="500" width="700" height="30" fill="white" stroke="black" stroke-width="1"/>
    <text x="400" y="520" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">数据补发流程 (通信中断后)</text>
    
    <line x1="200" y1="550" x2="600" y2="570" stroke="black" stroke-width="1.5"/>
    <polyline points="590,565 600,570 590,575" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="550" font-family="Arial" font-size="12" text-anchor="middle">9. 补发数据开始(0x48, 批次开始)</text>
    
    <line x1="200" y1="590" x2="600" y2="610" stroke="black" stroke-width="1.5"/>
    <polyline points="590,605 600,610 590,615" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="590" font-family="Arial" font-size="12" text-anchor="middle">10. 批量补发数据包(0x48)</text>
    
    <line x1="200" y1="630" x2="600" y2="650" stroke="black" stroke-width="1.5"/>
    <polyline points="590,645 600,650 590,655" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="630" font-family="Arial" font-size="12" text-anchor="middle">11. 补发数据结束(0x48, 批次结束)</text>
    
    <line x1="600" y1="670" x2="200" y2="690" stroke="black" stroke-width="1.5"/>
    <polyline points="210,685 200,690 210,695" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="670" font-family="Arial" font-size="12" text-anchor="middle">12. 批量确认应答(0x41)</text>
    
    <!-- 数据查询流程 -->
    <rect x="50" y="710" width="700" height="30" fill="white" stroke="black" stroke-width="1"/>
    <text x="400" y="730" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">数据查询流程</text>
    
    <line x1="600" y1="750" x2="200" y2="770" stroke="black" stroke-width="1.5" stroke-dasharray="5,3"/>
    <polyline points="210,765 200,770 210,775" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="750" font-family="Arial" font-size="12" text-anchor="middle">13. 数据查询请求(0x49)</text>
    
    <line x1="200" y1="790" x2="600" y2="810" stroke="black" stroke-width="1.5"/>
    <polyline points="590,805 600,810 590,815" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="790" font-family="Arial" font-size="12" text-anchor="middle">14. 数据查询响应(0x4A)</text>
    
    <!-- 心跳维持 -->
    <rect x="50" y="830" width="700" height="30" fill="white" stroke="black" stroke-width="1"/>
    <text x="400" y="850" font-family="Arial" font-size="12" text-anchor="middle" font-weight="bold">心跳维持</text>
    
    <line x1="200" y1="870" x2="600" y2="890" stroke="black" stroke-width="1.5"/>
    <polyline points="590,885 600,890 590,895" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="870" font-family="Arial" font-size="12" text-anchor="middle">15. 企业心跳(0x42)</text>
    
    <line x1="600" y1="910" x2="200" y2="930" stroke="black" stroke-width="1.5"/>
    <polyline points="210,925 200,930 210,935" fill="none" stroke="black" stroke-width="1.5"/>
    <text x="400" y="910" font-family="Arial" font-size="12" text-anchor="middle">16. 平台通用应答(0x41)</text>
  </svg>
  
  <h3>数据补发与查询流程</h3>
  <svg width="800" height="550" xmlns="http://www.w3.org/2000/svg">
    <!-- 背景和边框 -->
    <rect width="800" height="550" fill="white" stroke="none"/>
    
    <!-- 标题 -->
    <text x="400" y="30" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">数据补发与查询详细流程</text>
    
    <!-- 数据补发流程图 -->
    <rect x="50" y="50" width="700" height="230" fill="white" stroke="black" stroke-width="2"/>
    <text x="400" y="75" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">数据补发机制</text>
    
    <!-- 补发流程图内容 -->
    <rect x="80" y="90" width="180" height="40" fill="white" stroke="black" stroke-width="1"/>
    <text x="170" y="115" font-family="Arial" font-size="12" text-anchor="middle">通信链路中断</text>
    
    <line x1="170" y1="130" x2="170" y2="150" stroke="black" stroke-width="1.5"/>
    <polyline points="165,140 170,150 175,140" fill="none" stroke="black" stroke-width="1.5"/>
    
    <rect x="80" y="150" width="180" height="40" fill="white" stroke="black" stroke-width="1"/>
    <text x="170" y="170" font-family="Arial" font-size="12" text-anchor="middle">客户端本地存储数据</text>
    <text x="170" y="185" font-family="Arial" font-size="10" text-anchor="middle">(按优先级保存)</text>
    
    <line x1="170" y1="190" x2="170" y2="210" stroke="black" stroke-width="1.5"/>
    <polyline points="165,200 170,210 175,200" fill="none" stroke="black" stroke-width="1.5"/>
    
    <rect x="80" y="210" width="180" height="40" fill="white" stroke="black" stroke-width="1"/>
    <text x="170" y="230" font-family="Arial" font-size="12" text-anchor="middle">链路恢复后批量补发</text>
    <text x="170" y="245" font-family="Arial" font-size="10" text-anchor="middle">(0x48命令, 批次机制)</text>
    
    <!-- 补发流程说明 -->
    <rect x="320" y="90" width="400" height="160" fill="white" stroke="black" stroke-width="1"/>
    <text x="330" y="110" font-family="Arial" font-size="12" text-anchor="start">数据补发机制:</text>
    <text x="330" y="130" font-family="Arial" font-size="11" text-anchor="start">1. 通信中断时本地保存未发送数据</text>
    <text x="330" y="150" font-family="Arial" font-size="11" text-anchor="start">2. 重连后以批次方式发送补发数据(0x48)</text>
    <text x="330" y="170" font-family="Arial" font-size="11" text-anchor="start">3. 批次包含: 批次开始包 + 多个数据包 + 批次结束包</text>
    <text x="330" y="190" font-family="Arial" font-size="11" text-anchor="start">4. 服务端返回批量确认应答</text>
    <text x="330" y="210" font-family="Arial" font-size="11" text-anchor="start">5. 补发过程不影响实时数据上报, 实时数据优先</text>
    <text x="330" y="230" font-family="Arial" font-size="11" text-anchor="start">6. 事件数据优先补发, 重要性排序: 高风险>中风险>低风险>周期数据</text>
    
<!-- 数据查询流程图 -->
<rect x="50" y="300" width="700" height="230" fill="white" stroke="black" stroke-width="2"/>
<text x="400" y="325" font-family="Arial" font-size="14" text-anchor="middle" font-weight="bold">数据查询机制</text>

<!-- 查询流程图内容 -->
<rect x="550" y="340" width="180" height="40" fill="white" stroke="black" stroke-width="1"/>
<text x="640" y="365" font-family="Arial" font-size="12" text-anchor="middle">服务端发起查询(0x49)</text>

<line x1="640" y1="380" x2="640" y2="400" stroke="black" stroke-width="1.5"/>
<polyline points="635,390 640,400 645,390" fill="none" stroke="black" stroke-width="1.5"/>

<rect x="550" y="400" width="180" height="40" fill="white" stroke="black" stroke-width="1"/>
<text x="640" y="420" font-family="Arial" font-size="12" text-anchor="middle">指定查询参数</text>
<text x="640" y="435" font-family="Arial" font-size="10" text-anchor="middle">(时间范围/VIN/数据类型等)</text>

<line x1="550" y1="420" x2="290" y2="420" stroke="black" stroke-width="1.5" stroke-dasharray="5,3"/>
<polyline points="300,415 290,420 300,425" fill="none" stroke="black" stroke-width="1.5"/>

<rect x="100" y="400" width="180" height="40" fill="white" stroke="black" stroke-width="1"/>
<text x="190" y="420" font-family="Arial" font-size="12" text-anchor="middle">客户端执行查询</text>
<text x="190" y="435" font-family="Arial" font-size="10" text-anchor="middle">(从本地存储检索数据)</text>

<line x1="190" y1="440" x2="190" y2="460" stroke="black" stroke-width="1.5"/>
<polyline points="185,450 190,460 195,450" fill="none" stroke="black" stroke-width="1.5"/>

<rect x="100" y="460" width="180" height="40" fill="white" stroke="black" stroke-width="1"/>
<text x="190" y="480" font-family="Arial" font-size="12" text-anchor="middle">返回查询结果(0x4A)</text>
<text x="190" y="495" font-family="Arial" font-size="10" text-anchor="middle">(即时/文件/分批响应)</text>

<line x1="280" y1="480" x2="550" y2="480" stroke="black" stroke-width="1.5"/>
<polyline points="540,475 550,480 540,485" fill="none" stroke="black" stroke-width="1.5"/>

<!-- 查询流程说明 -->
<rect x="320" y="340" width="200" height="130" fill="white" stroke="black" stroke-width="1"/>
<text x="330" y="360" font-family="Arial" font-size="12" text-anchor="start">查询参数包括:</text>
<text x="330" y="380" font-family="Arial" font-size="11" text-anchor="start">• 查询流水号</text>
<text x="330" y="400" font-family="Arial" font-size="11" text-anchor="start">• 查询优先级(普通/优先/紧急)</text>
<text x="330" y="420" font-family="Arial" font-size="11" text-anchor="start">• 查询类型(流程/事件/统计)</text>
<text x="330" y="440" font-family="Arial" font-size="11" text-anchor="start">• 时间范围</text>
<text x="330" y="460" font-family="Arial" font-size="11" text-anchor="start">• 可选VIN码</text>
  </svg>
</body>
</html>